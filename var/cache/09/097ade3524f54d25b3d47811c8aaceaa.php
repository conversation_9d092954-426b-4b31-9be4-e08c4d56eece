<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* index.html */
class __TwigTemplate_ef04ad2c68b94205771183a6e9592256 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context)
    {
        // line 1
        return "layout.html";
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        $this->parent = $this->loadTemplate("layout.html", "index.html", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
    }

    // line 2
    public function block_content($context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 3
        yield "


        <!-- Price Table Area -->
    <section class=\"price-table-area section-padding-100-50 bg-img\"
        style=\"background-image:url(img/bg-img/bg-shape.png)\">
        <div class=\"container\">
            <div class=\"row justify-content-center\">
                <!-- Heading -->
                <div class=\"col-lg-7\">
                    <div class=\"heading-title text-center\">
                        <h2>Schoolworks에서 제공하는 서비스를 사용해 보세요.</h2>
                        <p>2024 <NAME_EMAIL></p>
                    </div>
                </div>
            </div>
                <div class=\"col-12\">
                    <div class=\"row\">
                        <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan mb-50\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading\">컴퓨터/프린터 등 IT 기기 수리 요청 게시판</span>
                                        </span>

                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"";
        // line 35
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "repair_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn3\" href=\"";
        // line 40
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "repair/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>

                        <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan active mb-50\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading active\">프린터 토너 요청 게시판</span>

                                       
                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                    
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"";
        // line 60
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "printer_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn2\" href=\"";
        // line 65
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "printer/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>

                        <!-- Single Pricing Plan-->
                
                        

                        <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan mb-50 wow\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading\">건물 수리 요청 게시판</span>

                                      
                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                   
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"";
        // line 89
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "crepair_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn2\" href=\"";
        // line 94
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "crepair/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>

                         <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan mb-50 wow\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading\">화장실 관리 요구서</span>

                                    
                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                   
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"";
        // line 114
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "toilet_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn2\" href=\"";
        // line 119
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "toilet/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>




                    </div>
                </div>
            </div>
        </div>

    </section>
    <!-- Price Table Area -->

   



";
        return; yield '';
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "index.html";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  190 => 119,  182 => 114,  159 => 94,  151 => 89,  124 => 65,  116 => 60,  93 => 40,  85 => 35,  51 => 3,  47 => 2,  36 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("{% extends 'layout.html' %}
{% block content %}



        <!-- Price Table Area -->
    <section class=\"price-table-area section-padding-100-50 bg-img\"
        style=\"background-image:url(img/bg-img/bg-shape.png)\">
        <div class=\"container\">
            <div class=\"row justify-content-center\">
                <!-- Heading -->
                <div class=\"col-lg-7\">
                    <div class=\"heading-title text-center\">
                        <h2>Schoolworks에서 제공하는 서비스를 사용해 보세요.</h2>
                        <p>2024 <NAME_EMAIL></p>
                    </div>
                </div>
            </div>
                <div class=\"col-12\">
                    <div class=\"row\">
                        <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan mb-50\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading\">컴퓨터/프린터 등 IT 기기 수리 요청 게시판</span>
                                        </span>

                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"{{ doc_root }}repair_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn3\" href=\"{{ doc_root }}repair/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>

                        <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan active mb-50\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading active\">프린터 토너 요청 게시판</span>

                                       
                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                    
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"{{ doc_root }}printer_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn2\" href=\"{{ doc_root }}printer/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>

                        <!-- Single Pricing Plan-->
                
                        

                        <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan mb-50 wow\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading\">건물 수리 요청 게시판</span>

                                      
                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                   
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"{{ doc_root }}crepair_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn2\" href=\"{{ doc_root }}crepair/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>

                         <!-- Single Pricing Plan-->
                        <div class=\"col-12 col-md-6 col-lg-4\">
                            <div class=\"pricing-card monthly-plan mb-50 wow\">
                                <div class=\"pricing-heading\">
                                    <div class=\"price-title\">
                                        <span class=\"price-heading\">화장실 관리 요구서</span>

                                    
                                    </div>
                                </div>
                                <!-- Pricing Details -->
                                <div class=\"pricing-desc\">
                                   
                                </div>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn\" href=\"{{ doc_root }}toilet_request_page\">게시판 생성하기</a>
                                
                                </div>
                                <p>
                                <div class=\"pricing-btn\">
                                    <a class=\"hero-btn2\" href=\"{{ doc_root }}toilet/1111111\">테스트 페이지</a>
                                </div>
                            </div>
                        </div>




                    </div>
                </div>
            </div>
        </div>

    </section>
    <!-- Price Table Area -->

   



{% endblock %}", "index.html", "/Users/<USER>/work_git/easyapps/templates/index.html");
    }
}
