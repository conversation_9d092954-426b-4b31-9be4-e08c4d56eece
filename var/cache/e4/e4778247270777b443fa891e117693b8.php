<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* printer_request_page.html */
class __TwigTemplate_e2ace358c61761f64e6d69f3ddc8d528 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context)
    {
        // line 1
        return "layout2.html";
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        $this->parent = $this->loadTemplate("layout2.html", "printer_request_page.html", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
    }

    // line 2
    public function block_content($context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 3
        yield "



    <br>


<!-- BEGIN LOADER -->


    <div class=\"auth-container d-flex\">

        <div class=\"container mx-auto align-self-center\">
    
            <div class=\"row\">
    
                <div class=\"col-xxl-4 col-xl-5 col-lg-5 col-md-8 col-12 d-flex flex-column align-self-center mx-auto\">
                    <div class=\"card mt-3 mb-3\">
                        <div class=\"card-body\">
    
                            <div class=\"row\">
                                <div class=\"col-md-12 mb-3\">
                                  <center>
                                    <h2>프린터 토너 서비스 신청
                                       
                                    </h2>
                                  </center> 
                                <form method=\"post\" action=\"";
        // line 30
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["doc_root"] ?? null), "html", null, true);
        yield "printer_request_complete\" > 
                                  ";
        // line 31
        if (($context["success"] ?? null)) {
            yield "<div class=\"alert alert-success\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["success"] ?? null), "html", null, true);
            yield "</div>";
        }
        // line 32
        yield "                                  ";
        if (($context["errors"] ?? null)) {
            yield "<div class=\"alert alert-danger\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, ($context["errors"] ?? null), "message", [], "any", false, false, false, 32), "html", null, true);
            yield "</div>";
        }
        // line 33
        yield "                                  </div>
                                  <div class=\"form-group mb-4\" >
                                        <label for=\"defaultForm-name\">1.학교명</label>(학교명을 입력하고 학교검색을 눌러주세요. 예:한국초)
                                        <input type=\"text\" class=\"form-control\" id=\"schoolSearch\", name=\"schoolSearch\" value=\"";
        // line 36
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["schoolSearch"] ?? null), "html", null, true);
        yield "\" required />
                                        <div class=\"errors\">";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, ($context["errors"] ?? null), "message", [], "any", false, false, false, 37), "html", null, true);
        yield "</div>
                                        
                                        ";
        // line 39
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["SCHUL_NM"] ?? null), "html", null, true);
        yield "
                                        <br>
                                      </div>
                                      <label for=\"defaultForm-name\">버튼을 클릭 후 해당 학교를 선택하세요</label>
                                      <div class=\"col-12\">
                                        <div class=\"mb-4\">
                                            <input type=\"button\" class=\"btn btn-secondary w-100 \" name=\"schoolBtn\" id=\"schoolBtn\" placeholder=\"학교검색\"
                                            value=\"2. 학교검색\" />
                                        </div>
                                        <label for=\"defaultForm-name\">아래 녹색 버튼의 해당 학교를 선택하세요</label>
                                      </div>
                                      <div class=\"schoolsearch12\" id=\"schoolsearch12\">
                                      </div>

                                      <div class=\"mb-3\">
                                          <input type=\"text\" name=\"SCHUL_NM\" id=\"SCHUL_NM\" value=\"";
        // line 54
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["SCHUL_NM"] ?? null), "html", null, true);
        yield "\" class=\"form-control\" placeholder=\"학교명\" readonly required onclick=\"getCheck()\">
                                          <br>
                                      </div>
                                      <div class=\"mb-3\">
                                          <input type=\"text\" name=\"SD_SCHUL_CODE\" id=\"SD_SCHUL_CODE\" value=\"";
        // line 58
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(($context["SD_SCHUL_CODE"] ?? null), "html", null, true);
        yield "\" class=\"form-control\" placeholder=\"표준학교코드\" readonly required>
                                          <br>
                                      </div>
                                 
                                  <div class=\"col-12\">
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"checkbox\" id=\"defaultGridCheck\" onclick=\"getCheck()\">
                                            <label class=\"form-check-label\" for=\"defaultGridCheck\">
                                                4. 아래 사항을 확인해주세요.
                                            </label>
                        
                                              <ul>
                                                    
                                                    <li><i class=\"fas fa-check\"></i> 요청 완료 페이지로 이동하지 않는 경우, 이미 신청이 완료되었을 수 있습니다. </li>
                                                    <li><i class=\"fas fa-check\"></i> 관리자에게 도움을 요청하세요. <EMAIL></li>
                                                    
                                                  </ul>
                                            </div>
                                        </div>
                                    </div>
          
                                  <div class=\"col-12\">
                                      <div class=\"mb-4\">
                                          <button id=\"submit\" class=\"btn btn-secondary w-100\" disabled=\"disabled\">5. 서비스 생성하기</button>
                                      </div>
                                  </div>
                                 
                               </form>
           
                            </div>
                            
                        </div>
                    </div>
                </div>
                
            </div>
            
        </div>

    </div>

    

  <script>


    //shcool search
    let render = document.getElementById(\"schoolBtn\").addEventListener('click', renderSchools);
    let schoolSearch = document.getElementById('schoolSearch').value;
    let html='';
    let container = document.querySelector('#schoolsearch12');

    async function getSchools(schoolSearch) {
        container.innerHTML = '';
        let url = `https://open.neis.go.kr/hub/schoolInfo?KEY=229bf2a993084683bdb67b50e475b50c&Type=json&pIndex=1&pSize=100&SCHUL_NM=\${schoolSearch}`;
        try {

        let res = await fetch(url);
        let data = await res.json()
        return await data.schoolInfo[1].row;
        } catch (error) {
        console.log(error);
        }
    }
    
    


    async function renderSchools() {
        
        let schoolSearch = document.querySelector('input[name=\"schoolSearch\"]').value;
        try {
            let schools = await getSchools(schoolSearch);
            
            schools.forEach(school => {
                let htmlSegment = `
                    <div class=\"mb-3\">
                        <button class=\"btn btn-success w-100\" id=\"selectBtn\" onclick=\"selectSchool('\${school.SCHUL_NM}', '\${school.SD_SCHUL_CODE}', '\${school.ATPT_OFCDC_SC_NM}')\">
                            3. \${school.SCHUL_NM} \${school.SD_SCHUL_CODE} \${school.ATPT_OFCDC_SC_NM}
                        </button>

                    </div>`;

            html += htmlSegment;
            
            });
            container.innerHTML = html;
            
        } catch (error) {
            console.log(error);
        }
        
    };

    async function selectSchool(schoolname, schoolcode, schoolstate) {
          alert(schoolstate + \" \" + schoolname + \"를 선택하셨습니다. 아래 4번으로 가셔서 확인 버튼을 눌러주세요.\");
          document.getElementById(\"SCHUL_NM\").value = schoolname;
          document.getElementById(\"SD_SCHUL_CODE\").value = schoolcode;
          html = '';
          let container = document.querySelector('#schoolsearch12');
            container.innerHTML = html;
          
          
    }

            //checkbox
    document.getElementById(\"submit\").disabled = true;
    function getCheck() {
      const selectedElements = document.querySelectorAll('input[class=form-check-input]:checked');
      //const selectedElements1 = document.querySelectorAll('input[class=orm-check-input]');
  
      console.log(selectedElements.length);
      if(selectedElements.length == 0) {
        document.getElementById(\"submit\").disabled = true;
      } else {
        document.getElementById(\"submit\").disabled = false;
        selectedElements.length ===1;
      }
    }
    

 </script>

";
        return; yield '';
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "printer_request_page.html";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  136 => 58,  129 => 54,  111 => 39,  106 => 37,  102 => 36,  97 => 33,  90 => 32,  84 => 31,  80 => 30,  51 => 3,  47 => 2,  36 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("{% extends 'layout2.html' %}
{% block content %}




    <br>


<!-- BEGIN LOADER -->


    <div class=\"auth-container d-flex\">

        <div class=\"container mx-auto align-self-center\">
    
            <div class=\"row\">
    
                <div class=\"col-xxl-4 col-xl-5 col-lg-5 col-md-8 col-12 d-flex flex-column align-self-center mx-auto\">
                    <div class=\"card mt-3 mb-3\">
                        <div class=\"card-body\">
    
                            <div class=\"row\">
                                <div class=\"col-md-12 mb-3\">
                                  <center>
                                    <h2>프린터 토너 서비스 신청
                                       
                                    </h2>
                                  </center> 
                                <form method=\"post\" action=\"{{ doc_root }}printer_request_complete\" > 
                                  {% if success %}<div class=\"alert alert-success\">{{ success }}</div>{% endif %}
                                  {% if errors %}<div class=\"alert alert-danger\">{{ errors.message }}</div>{% endif %}
                                  </div>
                                  <div class=\"form-group mb-4\" >
                                        <label for=\"defaultForm-name\">1.학교명</label>(학교명을 입력하고 학교검색을 눌러주세요. 예:한국초)
                                        <input type=\"text\" class=\"form-control\" id=\"schoolSearch\", name=\"schoolSearch\" value=\"{{ schoolSearch }}\" required />
                                        <div class=\"errors\">{{ errors.message }}</div>
                                        
                                        {{ SCHUL_NM }}
                                        <br>
                                      </div>
                                      <label for=\"defaultForm-name\">버튼을 클릭 후 해당 학교를 선택하세요</label>
                                      <div class=\"col-12\">
                                        <div class=\"mb-4\">
                                            <input type=\"button\" class=\"btn btn-secondary w-100 \" name=\"schoolBtn\" id=\"schoolBtn\" placeholder=\"학교검색\"
                                            value=\"2. 학교검색\" />
                                        </div>
                                        <label for=\"defaultForm-name\">아래 녹색 버튼의 해당 학교를 선택하세요</label>
                                      </div>
                                      <div class=\"schoolsearch12\" id=\"schoolsearch12\">
                                      </div>

                                      <div class=\"mb-3\">
                                          <input type=\"text\" name=\"SCHUL_NM\" id=\"SCHUL_NM\" value=\"{{ SCHUL_NM }}\" class=\"form-control\" placeholder=\"학교명\" readonly required onclick=\"getCheck()\">
                                          <br>
                                      </div>
                                      <div class=\"mb-3\">
                                          <input type=\"text\" name=\"SD_SCHUL_CODE\" id=\"SD_SCHUL_CODE\" value=\"{{ SD_SCHUL_CODE }}\" class=\"form-control\" placeholder=\"표준학교코드\" readonly required>
                                          <br>
                                      </div>
                                 
                                  <div class=\"col-12\">
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"checkbox\" id=\"defaultGridCheck\" onclick=\"getCheck()\">
                                            <label class=\"form-check-label\" for=\"defaultGridCheck\">
                                                4. 아래 사항을 확인해주세요.
                                            </label>
                        
                                              <ul>
                                                    
                                                    <li><i class=\"fas fa-check\"></i> 요청 완료 페이지로 이동하지 않는 경우, 이미 신청이 완료되었을 수 있습니다. </li>
                                                    <li><i class=\"fas fa-check\"></i> 관리자에게 도움을 요청하세요. <EMAIL></li>
                                                    
                                                  </ul>
                                            </div>
                                        </div>
                                    </div>
          
                                  <div class=\"col-12\">
                                      <div class=\"mb-4\">
                                          <button id=\"submit\" class=\"btn btn-secondary w-100\" disabled=\"disabled\">5. 서비스 생성하기</button>
                                      </div>
                                  </div>
                                 
                               </form>
           
                            </div>
                            
                        </div>
                    </div>
                </div>
                
            </div>
            
        </div>

    </div>

    

  <script>


    //shcool search
    let render = document.getElementById(\"schoolBtn\").addEventListener('click', renderSchools);
    let schoolSearch = document.getElementById('schoolSearch').value;
    let html='';
    let container = document.querySelector('#schoolsearch12');

    async function getSchools(schoolSearch) {
        container.innerHTML = '';
        let url = `https://open.neis.go.kr/hub/schoolInfo?KEY=229bf2a993084683bdb67b50e475b50c&Type=json&pIndex=1&pSize=100&SCHUL_NM=\${schoolSearch}`;
        try {

        let res = await fetch(url);
        let data = await res.json()
        return await data.schoolInfo[1].row;
        } catch (error) {
        console.log(error);
        }
    }
    
    


    async function renderSchools() {
        
        let schoolSearch = document.querySelector('input[name=\"schoolSearch\"]').value;
        try {
            let schools = await getSchools(schoolSearch);
            
            schools.forEach(school => {
                let htmlSegment = `
                    <div class=\"mb-3\">
                        <button class=\"btn btn-success w-100\" id=\"selectBtn\" onclick=\"selectSchool('\${school.SCHUL_NM}', '\${school.SD_SCHUL_CODE}', '\${school.ATPT_OFCDC_SC_NM}')\">
                            3. \${school.SCHUL_NM} \${school.SD_SCHUL_CODE} \${school.ATPT_OFCDC_SC_NM}
                        </button>

                    </div>`;

            html += htmlSegment;
            
            });
            container.innerHTML = html;
            
        } catch (error) {
            console.log(error);
        }
        
    };

    async function selectSchool(schoolname, schoolcode, schoolstate) {
          alert(schoolstate + \" \" + schoolname + \"를 선택하셨습니다. 아래 4번으로 가셔서 확인 버튼을 눌러주세요.\");
          document.getElementById(\"SCHUL_NM\").value = schoolname;
          document.getElementById(\"SD_SCHUL_CODE\").value = schoolcode;
          html = '';
          let container = document.querySelector('#schoolsearch12');
            container.innerHTML = html;
          
          
    }

            //checkbox
    document.getElementById(\"submit\").disabled = true;
    function getCheck() {
      const selectedElements = document.querySelectorAll('input[class=form-check-input]:checked');
      //const selectedElements1 = document.querySelectorAll('input[class=orm-check-input]');
  
      console.log(selectedElements.length);
      if(selectedElements.length == 0) {
        document.getElementById(\"submit\").disabled = true;
      } else {
        document.getElementById(\"submit\").disabled = false;
        selectedElements.length ===1;
      }
    }
    

 </script>

{% endblock %}", "printer_request_page.html", "/Users/<USER>/work_git/easyapps/templates/printer_request_page.html");
    }
}
