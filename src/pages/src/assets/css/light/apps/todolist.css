/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.layout-px-spacing {
  min-height: calc(100vh - 142px) !important;
}

.mail-box-container {
  position: relative;
  display: flex;
  border-radius: 8px;
  background: #fff;
  border: 1px solid #e0e6ed;
}

.mail-overlay {
  display: none;
  position: absolute;
  width: 100vw;
  height: 100%;
  background: #3b3f5c !important;
  z-index: 4 !important;
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
.mail-overlay.mail-overlay-show {
  display: block;
  opacity: 0.7;
}

.tab-title {
  position: relative;
  padding: 20px 15px;
  max-width: 240px;
  border-right: 1px solid #e0e6ed;
}
.tab-title .row {
  --bs-gutter-x:1.8rem;
}
.tab-title svg.feather-clipboard {
  color: #4361ee;
  fill: none;
  margin-bottom: 13px;
}
.tab-title h5 {
  position: relative;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 3px;
  color: #3b3f5c;
}
.tab-title #addTask {
  position: absolute;
  font-size: 14px;
  padding: 9px 20px;
  border: none;
  color: #191e3a;
  bottom: 32px;
  left: 17%;
  box-shadow: none;
}
.tab-title #addTask svg {
  margin-right: 5px;
}
.tab-title.mail-menu-show {
  left: 0;
  width: 100%;
  min-width: 190px;
  height: 100%;
}
.tab-title hr {
  border-top: 1px solid #ebedf2;
  max-width: 54px;
}
.tab-title .todoList-sidebar-scroll {
  position: relative;
  width: 100%;
  height: calc(100vh - 318px);
}
.tab-title .nav-pills .nav-link.active {
  background-color: transparent;
  color: #191e3a;
  background: #ebedf2;
  padding: 10px 12px 10px 14px;
}
.tab-title .nav-pills .nav-link.active svg {
  color: #4361ee;
  fill: #fff;
}
.tab-title .nav-pills a.nav-link {
  position: relative;
  font-weight: 700;
  color: #888ea8;
  border-radius: 0;
  padding: 15px 12px 15px 14px;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
}
.tab-title .nav-pills .nav-link .badge {
  border-radius: 50%;
  position: absolute;
  right: 24px;
  padding: 2px 5px;
  height: 24px;
  width: 23px;
  font-weight: 700;
  border: 2px solid #e0e6ed;
  transform: none;
}
.tab-title .nav-pills .nav-link.active .badge {
  border: none;
  padding: 0 !important;
  font-size: 15px;
  top: 11px;
  color: #191e3a !important;
}
.tab-title .nav-pills a.nav-link.active:hover {
  color: #0e1726;
}
.tab-title .nav-pills a.nav-link.active:hover svg {
  color: #0e1726;
}
.tab-title .nav-pills a.nav-link:hover svg {
  fill: none;
}
.tab-title .nav-pills a.nav-link svg {
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 21px;
  height: 21px;
  fill: none;
}
.tab-title .nav-pills .nav-link#all-list .badge {
  color: #607d8b;
  border-color: #e0e6ed;
}
.tab-title .nav-pills .nav-link#todo-task-done .badge {
  color: #2196f3;
  border-color: #2196f3;
}
.tab-title .nav-pills .nav-link#todo-task-important .badge {
  color: #e2a03f;
  border-color: #e2a03f;
}

/*
=====================
    Todo Inbox
=====================
*/
.todo-inbox {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  max-width: 100%;
  width: 100%;
}
.todo-inbox .search {
  display: flex;
}
.todo-inbox .search input {
  border: none;
  padding: 12px 13px 12px 13px;
  border-bottom: 1px solid #e0e6ed;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  color: #009688;
  transition: none;
}
.todo-inbox .mail-menu {
  margin: 7px 13px 7px 13px;
  width: 25px;
  border-radius: 0;
  color: #515365;
  align-self: center;
  border-bottom: 1px solid #191e3a;
}
.todo-inbox .todo-item-inner {
  display: flex;
}
.todo-inbox .message-box {
  background: #fff;
  padding: 0 0 5px 0;
}

.todo-box-scroll {
  position: relative;
  margin: auto;
  width: 100%;
  overflow: auto;
  height: calc(100vh - 200px);
}

.todo-inbox .todo-item {
  cursor: pointer;
  position: relative;
}
.todo-inbox .todo-item:not(:last-child) {
  border-bottom: 1px solid #e0e6ed;
}
.todo-inbox .todo-item.todo-task-trash {
  display: none;
}
.todo-inbox .todo-item.todo-task-trash.trash-show {
  display: block;
}
.todo-inbox .todo-item .todo-item-inner .n-chk {
  padding: 15px 10px 15px 10px;
  align-self: center;
}
.todo-inbox .todo-item .todo-item-inner .todo-content {
  width: 100%;
  padding: 15px 10px 15px 10px;
  align-self: center;
}
.todo-inbox .todo-item .todo-item-inner .todo-heading {
  font-size: 18px;
  font-weight: 600;
  color: #3b3f5c;
  margin-bottom: 0;
  -webkit-transition: transform 0.35s ease;
  transition: transform 0.35s ease;
}
.todo-inbox .todo-item .todo-item-inner:hover .todo-heading {
  -webkit-transform: translateY(0) scale(1.01);
  transform: translateY(0) scale(1.01);
}
.todo-inbox .todo-item .todo-item-inner p.todo-text {
  font-size: 14px;
  margin-bottom: 0;
  color: #eaeaec;
  font-weight: 600;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: calc(100vw - 884px);
  display: none;
}

body.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
  max-width: 509px;
}

.todo-inbox .todo-item .todo-item-inner:hover .todo-text {
  -webkit-transform: translateY(0) scale(1.01);
  transform: translateY(0) scale(1.01);
}
.todo-inbox .todo-item .todo-item-inner p.meta-date {
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
  color: #888ea8;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.todo-inbox .todo-item .todo-item-inner:hover p.meta-date {
  -webkit-transform: translateY(0) scale(1.01);
  transform: translateY(0) scale(1.01);
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown {
  float: right;
  padding: 15px 10px 15px 10px;
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle {
  font-size: 20px;
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle.danger svg {
  color: #e7515a;
  fill: rgba(231, 81, 90, 0.19);
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle.warning svg {
  color: #e2a03f;
  fill: rgba(233, 176, 43, 0.19);
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle.primary svg {
  color: #2196f3;
  fill: rgba(33, 150, 243, 0.19);
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu.show {
  top: 32px !important;
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.dropdown-item.active, .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.dropdown-item:active {
  background: transparent;
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a svg {
  font-size: 19px;
  font-weight: 700;
  margin-right: 7px;
  vertical-align: middle;
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.danger svg {
  color: #e7515a;
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.warning svg {
  color: #e2a03f;
}
.todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.primary svg {
  color: #2196f3;
}
.todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .permanent-delete, .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .revive {
  display: none;
}
.todo-inbox .todo-item.todo-task-trash .n-chk {
  display: none;
}
.todo-inbox .todo-item.todo-task-trash .todo-item-inner .todo-content {
  width: 100%;
  padding: 20px 14px 20px 14px;
}
.todo-inbox .todo-item.todo-task-trash .todo-item-inner .priority-dropdown .dropdown-menu {
  display: none;
}
.todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .edit, .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .important, .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .delete {
  display: none;
}
.todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .permanent-delete, .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .revive {
  display: block;
}
.todo-inbox .todo-item .todo-item-inner .action-dropdown {
  float: right;
  padding: 15px 10px 15px 10px;
}
.todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu.show {
  top: 32px !important;
}
.todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .dropdown-item.active, .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}
.todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-toggle svg {
  width: 21px;
  height: 21px;
  margin-top: 5px;
  color: #888ea8;
}
.todo-inbox .todo-item .todo-item-inner .action-dropdown .show .dropdown-toggle svg {
  color: #bfc9d4;
}
.todo-inbox .todo-item.todo-task-done .todo-item-inner .todo-heading {
  text-decoration: line-through;
  color: #888ea8;
}
.todo-inbox .todo-item.todo-task-done .todo-item-inner p.meta-date, .todo-inbox .todo-item.todo-task-done .todo-item-inner p.todo-text {
  text-decoration: line-through;
}

#todoShowListItem .task-text {
  position: relative;
  max-height: 260px;
  padding: 0 16px;
}

.compose-box {
  border-radius: 8px;
}

#todoShowListItem .compose-content h5 {
  margin-bottom: 19px;
  padding-bottom: 19px;
  border-bottom: 1px solid #191e3a;
}

.compose-box .compose-content h5 {
  font-weight: 700;
  font-size: 18px;
  color: #bfc9d4;
  text-align: center;
  margin-bottom: 35px;
}
.compose-box .compose-content .task-text p {
  word-break: break-word;
}
.compose-box .compose-content .task-text img {
  max-width: 100%;
}
.compose-box .compose-content form svg {
  align-self: center;
  font-size: 19px;
  margin-right: 14px;
  color: #009688;
  font-weight: 600;
}
.compose-box .compose-content form #taskdescription {
  height: 170px;
}
.compose-box .compose-content form .validation-text {
  display: none;
  color: #e7515a;
  font-weight: 600;
  text-align: left;
  margin-top: 6px;
  font-size: 12px;
  letter-spacing: 1px;
}
.compose-box .compose-content form #editor-container h1, .compose-box .compose-content form #editor-container p {
  color: #3b3f5c;
}

@media (max-width: 767px) {
  .todo-inbox {
    display: block;
  }
  .todo-inbox .message-box {
    width: 100%;
    margin-bottom: 40px;
  }
}
@media (min-width: 1400px) {
  body.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
    width: calc(100vw - 716px);
    max-width: 1037px;
  }
}
@media (max-width: 1199px) {
  body.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
    max-width: calc(100vw - 667px);
  }
}
@media (max-width: 991px) {
  body.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
    max-width: calc(100vw - 228px);
  }
  .mail-box-container {
    overflow-x: hidden;
    overflow-y: auto;
  }
  .todo-inbox .search {
    border-bottom: 1px solid #e0e6ed;
  }
  .todo-inbox .mail-menu {
    border-bottom: none;
  }
  .todo-inbox .search input {
    border-right: 1px solid #e0e6ed;
    border-bottom: none;
  }
  .todo-inbox .todo-item .todo-item-inner p.todo-text {
    max-width: calc(100vw - 228px);
  }
  .tab-title {
    position: absolute;
    z-index: 4;
    left: -100px;
    width: 0;
    background: #fff;
  }
  .todo-inbox {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
}
@media (max-width: 575px) {
  .todo-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body > div {
    display: block;
  }
  .todo-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .meta-time {
    margin-bottom: 0;
    float: none;
  }
}
/*
=====================
    IE Support
=====================
*/
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /* IE10+ CSS styles go here */
  .tab-title {
    width: 100%;
  }
}
/*
=====================
    Mozilla Support 
=====================
*/
@-moz-document url-prefix() {
  .todo-inbox .todo-item .todo-item-inner .todo-content {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
