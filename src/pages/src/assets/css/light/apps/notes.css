/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.main-container, #content {
  min-height: auto;
}

/*
    App Note Container
*/
.app-note-container {
  position: relative;
  display: flex;
}
.app-note-container .tab-title {
  max-width: 210px;
  width: 100%;
}

.note-sidebar-scroll {
  position: relative;
  margin: auto;
  width: 100%;
  overflow: auto;
  height: calc(100vh - 202px);
}

/*
    Group section 
*/
.group-section {
  font-weight: 600;
  font-size: 15px;
  color: #506690;
  letter-spacing: 1px;
  margin-top: 25px;
  margin-bottom: 13px;
  padding: 9px 20px;
}
.group-section svg {
  color: #506690;
  margin-right: 6px;
  vertical-align: text-top;
  width: 20px;
  height: 20px;
}

.app-note-overlay {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  background: #3b3f5c !important;
  z-index: 4 !important;
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
.app-note-overlay.app-note-overlay-show {
  display: block;
  opacity: 0.7;
}

/*
    Tab Title
*/
.tab-title.mail-menu-show {
  left: 0;
  width: 100%;
  min-width: 190px;
  height: 100%;
}
.tab-title hr {
  border-top: 1px solid #0e1726;
  max-width: 54px;
  margin: 0 auto;
  margin-top: 25px;
  margin-bottom: 25px;
}
.tab-title .nav-pills .nav-link.active, .tab-title .nav-pills .show > .nav-link {
  background-color: #ebedf2;
  color: #3b3f5c;
  font-weight: 600;
  border-left: 3px solid #3b3f5c;
  border-radius: 0;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  transition: none;
}
.tab-title .nav-pills a.nav-link {
  position: relative;
  font-weight: 600;
  color: #3b3f5c;
  padding: 9px 20px;
  cursor: pointer;
  font-size: 14px;
  border-radius: 8px;
}
.tab-title .nav-pills a.nav-link svg {
  margin-right: 7px;
  width: 18px;
  height: 18px;
  vertical-align: sub;
}
.tab-title .nav-pills a.nav-link .mail-badge {
  background: #eceffe;
  border-radius: 50%;
  position: absolute;
  right: 8px;
  padding: 4px 7px;
  height: 24px;
  width: 23px;
  color: #4361ee;
  font-weight: 600;
}
.tab-title .nav-pills.group-list .nav-item a {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  padding: 9px 15px 9px 50px;
  color: #3b3f5c;
  letter-spacing: 1px;
}
.tab-title .nav-pills.group-list .nav-item a[class*=g-dot-]:before {
  position: absolute;
  padding: 4px;
  content: "";
  border-radius: 50%;
  top: 14px;
  left: 20px;
  border: 1px solid #515365;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-danger:before {
  background: #e7515b;
  border-color: #e7515a;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-primary:before {
  background: #00ab55;
  border-color: #00ab55;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-warning:before {
  background: #e2a13f;
  border-color: #e2a03f;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-success:before {
  background: #805dca;
  border-color: #805dca;
}
.tab-title .nav-pills .nav-item .dropdown-menu {
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  padding: 0;
  border: none;
}
.tab-title li.mail-labels a.dropdown-item {
  font-size: 13px;
  font-weight: 700;
  padding: 8px 18px;
}
.tab-title li.mail-labels a.dropdown-item:hover {
  background-color: #fff;
  color: #4361ee;
}
.tab-title li.mail-labels .label:after {
  position: absolute;
  content: "";
  height: 6px;
  width: 6px;
  border-radius: 50%;
  right: 15px;
  top: 43%;
}

/*Mail Labels*/
/*
    Note container
*/
.note-container {
  padding: 0 0 0 15px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.note-container .switch {
  text-align: right;
  margin-bottom: 43px;
}
.note-container .switch .active-view {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.2392156863);
}
.note-container .switch .view-list, .note-container .switch .view-grid {
  padding: 10px;
  background: #fff;
  border-radius: 5px;
  cursor: pointer;
  color: #515365;
  box-shadow: 0px 2px 4px rgba(126, 142, 177, 0.12);
  width: 43px;
  height: 41px;
  fill: rgba(0, 23, 55, 0.08);
}

/* 
    Note Container
*/
.note-content {
  min-height: 135px;
  margin-bottom: 15px;
}

.note-container.note-grid .note-item {
  padding-right: 15px;
  padding-left: 15px;
}
.note-container.note-grid .note-item .note-inner-content {
  border-radius: 4px;
  width: 100%;
  position: relative;
  padding: 16px 16px 16px 16px;
  margin-right: 0;
  margin-bottom: 18px;
  border-radius: 10px;
  background: #fff;
  width: 100%;
  border: 1px solid #e0e6ed;
}
.note-container.note-grid .note-item .note-inner-content .note-title {
  font-size: 16px;
  font-weight: 500;
  color: #0e1726;
  margin-bottom: 0px;
  letter-spacing: 0px;
}
.note-container.note-grid .note-item .note-inner-content .meta-time {
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 12px;
  color: #506690;
  display: inline-block;
  border-radius: 4px;
}
.note-container.note-grid .note-item .note-inner-content .note-description {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 0;
  letter-spacing: 0px;
  word-wrap: break-word;
  color: #3b3f5c;
}
.note-container.note-grid .note-item .note-inner-content .note-action {
  display: inline-block;
}
.note-container.note-grid .note-item .note-inner-content .note-action .fav-note, .note-container.note-grid .note-item .note-inner-content .note-action .delete-note {
  padding: 4px;
  border-radius: 5px;
  cursor: pointer;
  color: #607d8b;
  width: 28px;
  height: 28px;
  stroke-width: 1.6;
}
.note-container.note-grid .note-item.note-fav .note-inner-content .note-action .fav-note {
  fill: rgba(255, 187, 68, 0.46);
  color: #ffbb44;
}
.note-container.note-grid .note-item .note-inner-content .note-action .fav-note:hover {
  color: #e2a03f;
}
.note-container.note-grid .note-item .note-inner-content .note-action .delete-note:hover {
  color: #e7515a;
}
.note-container.note-grid .note-item .note-inner-content .note-footer {
  display: inline-block;
  float: right;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags {
  display: inline-block;
  position: relative;
  padding: 4px 6px;
  border-radius: 4px;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags [class*=g-dot-] {
  content: "";
  background: transparent;
  border-radius: 50%;
  display: inline-block;
  height: 10px;
  width: 10px;
  vertical-align: middle;
  display: none;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags .g-dot-personal {
  background: #00ab55;
  border-color: #00ab55;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags .g-dot-work {
  background: #e2a13f;
  border-color: #e2a03f;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags .g-dot-social {
  background: #805dca;
  border-color: #805dca;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags .g-dot-important {
  background: #e7515b;
  border-color: #e7515a;
}
.note-container.note-grid .note-item.note-personal .note-inner-content .note-footer .tags .g-dot-personal, .note-container.note-grid .note-item.note-work .note-inner-content .note-footer .tags .g-dot-work, .note-container.note-grid .note-item.note-social .note-inner-content .note-footer .tags .g-dot-social, .note-container.note-grid .note-item.note-important .note-inner-content .note-footer .tags .g-dot-important {
  display: inline-block;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector {
  display: inline-block;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu {
  min-width: 8rem;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu a {
  font-size: 14px;
  padding: 3px 35px;
  letter-spacing: 0px;
  color: #515365;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu .dropdown-item.active, .note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu .dropdown-item:active {
  background: transparent;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu [class*=g-dot-]:before {
  content: "";
  position: absolute;
  padding: 4px;
  border-radius: 50%;
  top: 7px;
  left: 10px;
  border: 1px solid #515365;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu a.g-dot-personal:before {
  background: #00ab55;
  border: 1px solid #00ab55;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu a.g-dot-work:before {
  background: #e2a13f;
  border: 1px solid #e2a03f;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu a.g-dot-social:before {
  background: #805dca;
  border: 1px solid #805dca;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .tags-selector .dropdown-menu a.g-dot-important:before {
  background: #e7515a;
  border: 1px solid #e7515a;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .nav-link {
  padding: 0;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .nav-link span {
  display: block;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .nav-link .feather-more-vertical {
  border-radius: 5px;
  cursor: pointer;
  color: #607d8b;
  width: 20px;
  height: 20px;
}
.note-container.note-grid .note-item .note-inner-content .note-footer .nav-link .feather-more-vertical:hover {
  color: #bfc9d4;
}
.note-container.note-grid .note-item.note-personal .note-inner-content .note-footer .nav-link .feather-more-vertical, .note-container.note-grid .note-item.note-work .note-inner-content .note-footer .nav-link .feather-more-vertical, .note-container.note-grid .note-item.note-social .note-inner-content .note-footer .nav-link .feather-more-vertical, .note-container.note-grid .note-item.note-important .note-inner-content .note-footer .nav-link .feather-more-vertical {
  display: none;
}

/*
    ===============
        Note Box
    ===============
*/
.notes-box .notes-content form .note-description {
  padding-top: 40px;
}

.hamburger {
  display: none;
}

/*
    Media Query
*/
@media (min-width: 1200px) {
  .note-container.note-grid .note-item {
    -ms-flex: 0 0 33%;
    flex: 0 0 33%;
    max-width: 33%;
  }
}
@media (min-width: 1920px) {
  .note-container.note-grid .note-item {
    -ms-flex: 0 0 24.666667%;
    flex: 0 0 24.666667%;
    max-width: 24.666667%;
  }
}
@media (max-width: 1199px) {
  .note-container {
    padding: 0;
  }
  .note-container.note-grid .note-item {
    -ms-flex: 0 0 49.333333%;
    flex: 0 0 49.333333%;
    max-width: 49.333333%;
  }
}
@media (max-width: 991px) {
  .app-notes {
    margin-top: 37px;
  }
  .app-note-container .tab-title {
    position: absolute;
    z-index: 4;
    left: -170px;
    width: 0;
  }
  .tab-title.note-menu-show {
    left: 0;
    width: 100%;
    min-width: 190px;
    min-height: 485px;
    border-radius: 0;
    border-bottom-right-radius: 8px;
    padding: 11px;
    background: #fff;
  }
  .note-sidebar-scroll {
    height: 100%;
  }
  .app-hamburger-container {
    text-align: right;
  }
  .hamburger {
    position: relative;
    top: -13px;
    padding: 6px 9px 6px 9px;
    font-size: 20px;
    color: #fff;
    align-self: center;
    display: inline-block;
    background-color: #515365;
    border-radius: 50%;
  }
}
@media (max-width: 575px) {
  .note-container {
    -webkit-column-count: 1;
    -moz-column-count: 1;
    column-count: 1;
  }
  .note-container.note-grid .note-item {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
