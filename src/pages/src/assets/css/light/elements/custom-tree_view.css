/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*  Tree View   */
.treeview {
  list-style: none;
  padding: 0;
  margin-bottom: 0;
  position: relative;
}
.treeview li p {
  font-size: 13px;
  color: #888ea8;
  font-weight: 500;
}
.treeview .tv-item .tv-header {
  padding: 6px 0;
  cursor: pointer;
  position: relative;
}
.treeview .tv-item .tv-header .tv-collapsible {
  display: flex;
}
.treeview .tv-item .tv-header .tv-collapsible .icon {
  margin-right: 6px;
  align-self: center;
}
.treeview .tv-item .tv-header .tv-collapsible .icon svg {
  width: 14px;
  height: 14px;
  transition: 0.5s;
}
.treeview .tv-item .tv-header .tv-collapsible .title {
  margin-bottom: 0;
  align-self: center;
  font-size: 14px;
  color: #3b3f5c;
  font-weight: 600;
}
.treeview .tv-item .tv-header .tv-collapsible:not(.collapsed) .icon svg.icon-tabler-chevron-right {
  transform: rotate(90deg);
  color: #3b3f5c;
}
.treeview .tv-item .treeview-collapse .treeview {
  position: relative;
}
.treeview .tv-item .treeview-collapse .treeview:before {
  content: "";
  position: absolute;
  height: calc(100% - 10px);
  width: 1px;
  /* background: #000; */
  top: 0;
  left: -24px;
  display: block;
  border-right: 1px dashed #888ea8;
  display: none;
}
.treeview .tv-item .treeview-collapse.show .treeview:before {
  display: block;
}
.treeview .treeview {
  margin-left: 30px;
  list-style: none;
  padding: 0;
}
.treeview .treeview .tv-item {
  position: relative;
}
.treeview .treeview .tv-item:before {
  content: "";
  position: absolute;
  height: 1px;
  width: 18px;
  /* background: #000; */
  left: -22px;
  top: 10px;
  border-bottom: 1px dashed #888ea8;
}
.treeview .treeview .tv-item.tv-folder:before {
  left: -22px;
  top: 18px;
}
.treeview .treeview .tv-item:not(.tv-folder) {
  padding-left: 5px;
}
.treeview.folder-structure .tv-item .tv-header {
  padding: 6px 0;
  cursor: pointer;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible {
  display: flex;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible .icon {
  margin-right: 6px;
  align-self: center;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible .icon svg {
  width: 20px;
  height: 20px;
  transition: 0.5s;
  color: #e2a03f;
  fill: #e2a03f;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible:not(.collapsed) .icon svg {
  color: #e2a03f;
  fill: rgba(226, 160, 63, 0.4);
}
.treeview.folder-structure.treeview .tv-item:not(.tv-folder) span.icon {
  margin-left: 5px;
}
.treeview.folder-structure.treeview .tv-item:not(.tv-folder) span.icon svg {
  color: #888ea8;
  fill: rgba(67, 97, 238, 0.2);
  width: 20px;
  height: 20px;
}
.treeview.folder-structure.treeview .tv-item:not(.tv-folder) p {
  display: inline-block;
}
.treeview.folder-structure.treeview .tv-item:before {
  top: 14px;
}
.treeview.folder-structure .tv-item .treeview-collapse .treeview:before {
  height: calc(100% - 16px);
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
