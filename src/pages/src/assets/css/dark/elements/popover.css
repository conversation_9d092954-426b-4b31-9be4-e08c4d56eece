/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .popovers-section h6 {
  color: #3b3f5c;
  font-size: 0.875rem;
  margin-top: 25px;
  margin-bottom: 20px;
}
body.dark .popover {
  background-color: #060818;
  border: 1px solid #060818;
  border-radius: 4px;
}
body.dark .popover .popover-header {
  border-radius: 0;
  background-color: #060818;
  color: #bfc9d4;
}
body.dark .popover .popover-body {
  background-color: #060818;
  color: #bfc9d4;
  padding: 0.5rem 0.75rem;
}

/*
	Popovers
*/
body.dark .popover-primary, body.dark .popover-success, body.dark .popover-info, body.dark .popover-danger, body.dark .popover-warning, body.dark .popover-secondary, body.dark .popover-dark {
  border-color: #060818;
}

/* 		popover Arrow 	*/
body.dark [data-popper-placement^=right] > .popover .popover-arrow:after, body.dark [data-popper-placement^=right] > .popover .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .bs-popover-bottom.popover .popover-arrow:after, body.dark .bs-popover-bottom.popover .popover-arrow:before {
  border-bottom-color: #060818;
}
body.dark .bs-popover-end.popover .popover-arrow:after, body.dark .bs-popover-end.popover .popover-arrow:before {
  border-right-color: #060818;
}
body.dark .bs-popover-start.popover .popover-arrow:after, body.dark .bs-popover-start.popover .popover-arrow:before {
  border-left-color: #060818;
}
body.dark .popover-primary .popover-arrow:after, body.dark .popover-primary .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-success .popover-arrow:after, body.dark .popover-success .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-info .popover-arrow:after, body.dark .popover-info .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-danger .popover-arrow:after, body.dark .popover-danger .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-warning .popover-arrow:after, body.dark .popover-warning .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-secondary .popover-arrow:after, body.dark .popover-secondary .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-dark .popover-arrow:after, body.dark .popover-dark .popover-arrow:before {
  border-top-color: #060818;
}

body.dark .bs-popover-auto[data-popper-placement^=top] > .popover-arrow:after, body.dark .bs-popover-auto[data-popper-placement^=top] > .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow:after, body.dark .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow:before {
  border-bottom-color: #060818;
}
body.dark .bs-popover-auto[data-popper-placement^=right] > .popover-arrow:after, body.dark .bs-popover-auto[data-popper-placement^=right] > .popover-arrow:before {
  border-right-color: #060818;
}
body.dark .bs-popover-auto[data-popper-placement^=left] > .popover-arrow:after, body.dark .bs-popover-auto[data-popper-placement^=left] > .popover-arrow:before {
  border-left-color: #060818;
}
body.dark .popover-primary .popover-arrow:after, body.dark .popover-primary .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-success .popover-arrow:after, body.dark .popover-success .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-info .popover-arrow:after, body.dark .popover-info .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-danger .popover-arrow:after, body.dark .popover-danger .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-warning .popover-arrow:after, body.dark .popover-warning .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-secondary .popover-arrow:after, body.dark .popover-secondary .popover-arrow:before {
  border-top-color: #060818;
}
body.dark .popover-dark .popover-arrow:after, body.dark .popover-dark .popover-arrow:before {
  border-top-color: #060818;
}

/* 		popover Header 		*/
body.dark .popover-primary .popover-header {
  background-color: #060818;
  border: none;
  color: #4361ee;
}
body.dark .popover-success .popover-header {
  background-color: #060818;
  border: none;
  color: #00ab55;
}
body.dark .popover-info .popover-header {
  background-color: #060818;
  border: none;
  color: #2196f3;
}
body.dark .popover-danger .popover-header {
  background-color: #060818;
  border: none;
  color: #e7515a;
}
body.dark .popover-warning .popover-header {
  background-color: #060818;
  border: none;
  color: #e2a03f;
}
body.dark .popover-secondary .popover-header {
  background-color: #060818;
  border: none;
  color: #805dca;
}
body.dark .popover-dark .popover-header {
  background-color: #060818;
  border: none;
  color: #3b3f5c;
}

/*  	Popover Body 	*/
body.dark .popover-primary .popover-body {
  background-color: #060818;
  color: #4361ee;
}
body.dark .popover-success .popover-body {
  background-color: #060818;
  color: #00ab55;
}
body.dark .popover-info .popover-body {
  background-color: #060818;
  color: #2196f3;
}
body.dark .popover-danger .popover-body {
  background-color: #060818;
  color: #e7515a;
}
body.dark .popover-warning .popover-body {
  background-color: #060818;
  color: #e2a03f;
}
body.dark .popover-secondary .popover-body {
  background-color: #060818;
  color: #805dca;
}
body.dark .popover-dark .popover-body {
  background-color: #060818;
  color: #3b3f5c;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
