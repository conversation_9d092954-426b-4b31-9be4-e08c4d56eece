/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*      Alert       */
body.dark .alert {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  margin-bottom: 15px;
  padding: 0.9375rem;
}
body.dark .alert .btn {
  margin-right: 27px;
}
body.dark .alert .btn:hover {
  box-shadow: none;
}
body.dark .alert .alert-icon svg {
  vertical-align: middle;
  width: 33px;
  height: 33px;
  stroke-width: 1.2;
}
body.dark .alert .btn-close {
  color: #fff;
  opacity: 1;
  width: 18px;
  background: transparent;
  padding: 13px 12px;
  box-shadow: none;
}
body.dark .alert .btn-close svg {
  width: 18px;
  height: 18px;
}

/*Default Alerts*/
body.dark .alert-primary {
  color: #fff;
  background-color: #4361ee;
  border-color: #4361ee;
}
body.dark .alert-warning {
  color: #fff;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .alert-success {
  color: #fff;
  background-color: #00ab55;
  border-color: #00ab55;
}
body.dark .alert-info {
  color: #fff;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .alert-danger {
  color: #fff;
  background-color: #e7515a;
  border-color: #e7515a;
}
body.dark .alert-dark {
  color: #fff;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

/*Outline Alerts*/
body.dark .alert-outline-primary {
  border-color: #4361ee;
  border-radius: 5px;
}
body.dark .alert-outline-warning {
  border-color: #dea82a;
  border-radius: 5px;
}
body.dark .alert-outline-success {
  border-color: #00ab55;
  border-radius: 5px;
}
body.dark .alert-outline-info {
  border-color: #009eda;
  border-radius: 5px;
}
body.dark .alert-outline-danger {
  border-color: #e7515a;
  border-radius: 5px;
}
body.dark .alert-outline-dark {
  border-color: #454656;
  border-radius: 5px;
}
body.dark .alert.alert-light .close {
  color: #0e1726;
}
body.dark .alert.solid-alert-3 .close, body.dark .alert.solid-alert-4 .close {
  color: #000;
}
body.dark .hide-default {
  display: none;
}

/*      Light Alert         */
body.dark .btn-light {
  border-color: transparent;
}
body.dark .alert-light-primary {
  color: #4361ee;
  background-color: #152143;
  border-color: rgba(67, 97, 238, 0.55);
}
body.dark .alert-light-primary svg.close {
  color: #4361ee;
}
body.dark .alert-light-warning {
  color: #e2a03f;
  background-color: #282625;
  border-color: rgba(226, 160, 63, 0.55);
}
body.dark .alert-light-warning svg.close {
  color: #e2a03f;
}
body.dark .alert-light-success {
  color: #00ab55;
  background-color: #0c272b;
  border-color: rgba(26, 188, 156, 0.55);
}
body.dark .alert-light-success svg.close {
  color: #00ab55;
}
body.dark .alert-light-info {
  color: #2196f3;
  background-color: #0b2f52;
  border-color: rgba(33, 150, 243, 0.55);
}
body.dark .alert-light-info svg.close {
  color: #2196f3;
}
body.dark .alert-light-danger {
  color: #e7515a;
  background-color: #2c1c2b;
  border-color: rgba(231, 81, 90, 0.55);
}
body.dark .alert-light-danger svg.close {
  color: #e7515a;
}
body.dark .alert-light-dark {
  color: #bfc9d4;
  background-color: #181e2e;
  border-color: rgba(59, 63, 92, 0.55);
}
body.dark .alert-light-dark svg.close {
  color: #3b3f5c;
}
body.dark .alert-light-dark svg:not(.close) {
  color: #3b3f5c !important;
}

/*  Background Alerts      */
body.dark .alert-background {
  color: #fff;
  background: #fff url(../../../img/ab-1.jpeg) no-repeat center center;
  background-size: cover;
  border: none;
}

/*  Gradient Alerts      */
body.dark .alert-gradient {
  color: #fff;
  border: none;
  background-size: cover;
  background-image: linear-gradient(135deg, #bc1a4e 0%, #004fe6 100%);
}

/* Custom Alerts */
/* Default */
body.dark .custom-alert-1 {
  background-color: rgba(0, 171, 85, 0.4196078431);
  border-radius: 5px;
  color: #fff;
}
body.dark .custom-alert-1 .btn-close {
  top: 9px;
}
body.dark .custom-alert-1 .alert-icon {
  margin-right: 25px;
}
body.dark .custom-alert-1 .media-body {
  display: flex;
  justify-content: space-between;
}
body.dark .custom-alert-1 .alert-text {
  margin-right: 10px;
}
body.dark .custom-alert-1 .alert-text strong, body.dark .custom-alert-1 .alert-text span {
  vertical-align: sub;
}

/*  Alert with Icon */
body.dark .alert-icon-left {
  border-left: 64px solid;
}
body.dark .alert-icon-left svg:not(.close) {
  color: #FFF;
  width: 4rem;
  left: -4rem;
  text-align: center;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body.dark .alert-icon-right {
  border-right: 64px solid;
}
body.dark .alert-icon-right svg:not(.close) {
  color: #FFF;
  width: 4rem;
  right: -4rem;
  text-align: center;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body.dark .alert-icon-right i {
  float: left;
  margin-right: 7px;
}
body.dark .alert[class*=alert-arrow-]:before {
  content: "";
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  border-left: 8px solid;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left-color: inherit;
  margin-top: -8px;
}
body.dark .alert.alert-arrow-right:before {
  left: auto;
  right: 0;
  border-left: 0;
  border-right: 8px solid;
  border-right-color: inherit;
}

@media (max-width: 575px) {
  body.dark .custom-alert-1 .media-body {
    display: block;
  }
  body.dark .alert .btn {
    margin-top: 8px;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
