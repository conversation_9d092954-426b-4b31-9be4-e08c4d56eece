/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .layout-px-spacing {
  min-height: calc(100vh - 142px) !important;
}
body.dark .mail-box-container {
  position: relative;
  display: flex;
  border-radius: 8px;
  background: #0e1726;
  -webkit-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  border: 1px solid #0e1726;
}
body.dark .mail-overlay {
  display: none;
  position: absolute;
  width: 100vw;
  height: 100%;
  background: #3b3f5c !important;
  z-index: 4 !important;
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
body.dark .mail-overlay.mail-overlay-show {
  display: block;
  opacity: 0.7;
}
body.dark .tab-title {
  position: relative;
  padding: 20px 15px;
  max-width: 240px;
  border-right: 1px solid #191e3a;
}
body.dark .tab-title .row {
  --bs-gutter-x:1.8rem;
}
body.dark .tab-title svg.feather-clipboard {
  color: #0e1726;
  fill: #009688;
  margin-bottom: 13px;
}
body.dark .tab-title h5 {
  position: relative;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 3px;
  color: #888ea8;
}
body.dark .tab-title #addTask {
  position: absolute;
  font-size: 14px;
  padding: 9px 20px;
  border: none;
  color: #191e3a;
  bottom: 32px;
  left: 17%;
  box-shadow: none;
}
body.dark .tab-title #addTask svg {
  margin-right: 5px;
}
body.dark .tab-title.mail-menu-show {
  left: 0;
  width: 100%;
  min-width: 190px;
  height: 100%;
}
body.dark .tab-title hr {
  border-top: 1px solid #ebedf2;
  max-width: 54px;
}
body.dark .tab-title .todoList-sidebar-scroll {
  position: relative;
  width: 100%;
  height: calc(100vh - 318px);
}
body.dark .tab-title .nav-pills .nav-link.active {
  background-color: transparent;
  color: #191e3a;
  background: #009688;
  padding: 10px 12px 10px 14px;
}
body.dark .tab-title .nav-pills .nav-link.active svg {
  color: #191e3a;
}
body.dark .tab-title .nav-pills a.nav-link {
  position: relative;
  font-weight: 700;
  color: #888ea8;
  border-radius: 0;
  padding: 15px 12px 15px 14px;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
}
body.dark .tab-title .nav-pills .nav-link .badge {
  border-radius: 50%;
  position: absolute;
  right: 24px;
  padding: 2px 5px;
  height: 24px;
  width: 23px;
  font-weight: 700;
  border: 2px solid #e0e6ed;
  transform: none;
}
body.dark .tab-title .nav-pills .nav-link.active .badge {
  border: none;
  padding: 0 !important;
  font-size: 15px;
  top: 11px;
  color: #191e3a !important;
}
body.dark .tab-title .nav-pills a.nav-link.active:hover {
  color: #0e1726;
}
body.dark .tab-title .nav-pills a.nav-link.active:hover svg {
  color: #0e1726;
}
body.dark .tab-title .nav-pills a.nav-link:hover svg {
  fill: rgba(136, 142, 168, 0.2588235294);
}
body.dark .tab-title .nav-pills a.nav-link svg {
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 21px;
  height: 21px;
  fill: rgba(0, 23, 55, 0.08);
}
body.dark .tab-title .nav-pills .nav-link#all-list .badge {
  color: #607d8b;
  border-color: #607d8b;
}
body.dark .tab-title .nav-pills .nav-link#todo-task-done .badge {
  color: #2196f3;
  border-color: #2196f3;
}
body.dark .tab-title .nav-pills .nav-link#todo-task-important .badge {
  color: #e2a03f;
  border-color: #e2a03f;
}

/*
=====================
    Todo Inbox
=====================
*/
body.dark .todo-inbox {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  max-width: 100%;
  width: 100%;
}
body.dark .todo-inbox .search {
  display: flex;
}
body.dark .todo-inbox .search input {
  border: none;
  padding: 12px 13px 12px 13px;
  border-bottom: 1px solid #191e3a;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  color: #009688;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  transition: none;
}
body.dark .todo-inbox .mail-menu {
  margin: 7px 13px 7px 13px;
  width: 25px;
  border-radius: 0;
  color: #515365;
  align-self: center;
  border-bottom: 1px solid #191e3a;
}
body.dark .todo-inbox .todo-item-inner {
  display: flex;
}
body.dark .todo-inbox .message-box {
  background: #fff;
  padding: 0 0 5px 0;
}
body.dark .todo-box-scroll {
  position: relative;
  margin: auto;
  width: 100%;
  overflow: auto;
  height: calc(100vh - 200px);
}
body.dark .todo-inbox .todo-item {
  cursor: pointer;
  position: relative;
}
body.dark .todo-inbox .todo-item:not(:last-child) {
  border-bottom: 1px solid #191e3a;
}
body.dark .todo-inbox .todo-item.todo-task-trash {
  display: none;
}
body.dark .todo-inbox .todo-item.todo-task-trash.trash-show {
  display: block;
}
body.dark .todo-inbox .todo-item .todo-item-inner .n-chk {
  padding: 15px 10px 15px 10px;
  align-self: center;
}
body.dark .todo-inbox .todo-item .todo-item-inner .todo-content {
  width: 100%;
  padding: 15px 10px 15px 10px;
  align-self: center;
}
body.dark .todo-inbox .todo-item .todo-item-inner .todo-heading {
  font-size: 18px;
  font-weight: 600;
  color: #bfc9d4;
  margin-bottom: 0;
  -webkit-transition: transform 0.35s ease;
  transition: transform 0.35s ease;
}
body.dark .todo-inbox .todo-item .todo-item-inner:hover .todo-heading {
  -webkit-transform: translateY(0) scale(1.01);
  transform: translateY(0) scale(1.01);
}
body.dark .todo-inbox .todo-item .todo-item-inner p.todo-text {
  font-size: 14px;
  margin-bottom: 0;
  color: #181e2e;
  font-weight: 600;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: calc(100vw - 884px);
  display: none;
}

body.dark.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
  max-width: 509px;
}

body.dark .todo-inbox .todo-item .todo-item-inner:hover .todo-text {
  -webkit-transform: translateY(0) scale(1.01);
  transform: translateY(0) scale(1.01);
}
body.dark .todo-inbox .todo-item .todo-item-inner p.meta-date {
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
  color: #888ea8;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
body.dark .todo-inbox .todo-item .todo-item-inner:hover p.meta-date {
  -webkit-transform: translateY(0) scale(1.01);
  transform: translateY(0) scale(1.01);
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown {
  float: right;
  padding: 15px 10px 15px 10px;
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle {
  font-size: 20px;
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle.danger svg {
  color: #e7515a;
  fill: rgba(231, 81, 90, 0.19);
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle.warning svg {
  color: #e2a03f;
  fill: rgba(233, 176, 43, 0.19);
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-toggle.primary svg {
  color: #2196f3;
  fill: rgba(33, 150, 243, 0.19);
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu.show {
  top: 32px !important;
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.dropdown-item.active, body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.dropdown-item:active {
  background: transparent;
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a svg {
  font-size: 19px;
  font-weight: 700;
  margin-right: 7px;
  vertical-align: middle;
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.danger svg {
  color: #e7515a;
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.warning svg {
  color: #e2a03f;
}
body.dark .todo-inbox .todo-item .todo-item-inner .priority-dropdown .dropdown-menu a.primary svg {
  color: #2196f3;
}
body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .permanent-delete, body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .revive {
  display: none;
}
body.dark .todo-inbox .todo-item.todo-task-trash .n-chk {
  display: none;
}
body.dark .todo-inbox .todo-item.todo-task-trash .todo-item-inner .todo-content {
  width: 100%;
  padding: 20px 14px 20px 14px;
}
body.dark .todo-inbox .todo-item.todo-task-trash .todo-item-inner .priority-dropdown .dropdown-menu {
  display: none;
}
body.dark .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .edit, body.dark .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .important, body.dark .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .delete {
  display: none;
}
body.dark .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .permanent-delete, body.dark .todo-inbox .todo-item.todo-task-trash .todo-item-inner .action-dropdown .dropdown-menu .revive {
  display: block;
}
body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown {
  float: right;
  padding: 15px 10px 15px 10px;
}
body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu.show {
  top: 32px !important;
}
body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .dropdown-item.active, body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}
body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown .dropdown-toggle svg {
  width: 21px;
  height: 21px;
  margin-top: 5px;
  color: #888ea8;
}
body.dark .todo-inbox .todo-item .todo-item-inner .action-dropdown .show .dropdown-toggle svg {
  color: #bfc9d4;
}
body.dark .todo-inbox .todo-item.todo-task-done .todo-item-inner .todo-heading {
  text-decoration: line-through;
  color: #888ea8;
}
body.dark .todo-inbox .todo-item.todo-task-done .todo-item-inner p.meta-date, body.dark .todo-inbox .todo-item.todo-task-done .todo-item-inner p.todo-text {
  text-decoration: line-through;
}
body.dark #todoShowListItem .task-text {
  position: relative;
  max-height: 260px;
  padding: 0 16px;
}
body.dark .compose-box {
  border-radius: 8px;
}
body.dark #todoShowListItem .compose-content h5 {
  margin-bottom: 19px;
  padding-bottom: 19px;
  border-bottom: 1px solid #191e3a;
}
body.dark .compose-box .compose-content h5 {
  font-weight: 700;
  font-size: 18px;
  color: #bfc9d4;
  text-align: center;
  margin-bottom: 35px;
}
body.dark .compose-box .compose-content .task-text p {
  word-break: break-word;
}
body.dark .compose-box .compose-content .task-text img {
  max-width: 100%;
}
body.dark .compose-box .compose-content form svg {
  align-self: center;
  font-size: 19px;
  margin-right: 14px;
  color: #009688;
  font-weight: 600;
}
body.dark .compose-box .compose-content form #taskdescription {
  height: 170px;
}
body.dark .compose-box .compose-content form .validation-text {
  display: none;
  color: #e7515a;
  font-weight: 600;
  text-align: left;
  margin-top: 6px;
  font-size: 12px;
  letter-spacing: 1px;
}
body.dark .compose-box .compose-content form #editor-container h1, body.dark .compose-box .compose-content form #editor-container p {
  color: #3b3f5c;
}
@media (max-width: 767px) {
  body.dark .todo-inbox {
    display: block;
  }
  body.dark .todo-inbox .message-box {
    width: 100%;
    margin-bottom: 40px;
  }
}

@media (min-width: 1400px) {
  body.dark.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
    width: calc(100vw - 716px);
    max-width: 1037px;
  }
}
@media (max-width: 1199px) {
  body.dark.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
    max-width: calc(100vw - 667px);
  }
}
@media (max-width: 991px) {
  body.dark.layout-boxed .todo-inbox .todo-item .todo-item-inner p.todo-text {
    max-width: calc(100vw - 228px);
  }
  body.dark .mail-box-container {
    overflow-x: hidden;
    overflow-y: auto;
  }
  body.dark .todo-inbox .search {
    border-bottom: 1px solid #191e3a;
  }
  body.dark .todo-inbox .mail-menu {
    border-bottom: none;
  }
  body.dark .todo-inbox .search input {
    border-right: 1px solid #191e3a;
    border-bottom: none;
  }
  body.dark .todo-inbox .todo-item .todo-item-inner p.todo-text {
    max-width: calc(100vw - 228px);
  }
  body.dark .tab-title {
    position: absolute;
    z-index: 4;
    left: -100px;
    width: 0;
    background: #0e1726;
  }
  body.dark .todo-inbox {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
}
@media (max-width: 575px) {
  body.dark .todo-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body > div {
    display: block;
  }
  body.dark .todo-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .meta-time {
    margin-bottom: 0;
    float: none;
  }
}
/*
=====================
    IE Support
=====================
*/
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  body.dark .tab-title {
    width: 100%;
  }
}

/*
=====================
    Mozilla Support 
=====================
*/
@-moz-document url-prefix() {
  body.dark .todo-inbox .todo-item .todo-item-inner .todo-content {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
