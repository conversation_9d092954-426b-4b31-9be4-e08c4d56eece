/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .modal-backdrop {
  background: linear-gradient(75deg, rgba(22, 28, 36, 0.48) 0%, rgb(22, 28, 36) 100%);
}
body.dark .modal-backdrop.show {
  opacity: 0.8;
}
body.dark .modal-content {
  border: none;
  border-radius: 6px;
  background: #0e1726;
  border: 1px solid #191e3a;
}
body.dark .modal-content hr {
  border-top: 1px solid #191e3a;
}
body.dark .modal-content .modal-header {
  padding: 12px 26px;
  border: none;
  border-bottom: 1px solid #191e3a;
}
body.dark .modal-content .modal-header h5 {
  font-weight: 600;
  font-size: 20px;
  letter-spacing: 1px;
}
body.dark .modal-content .modal-header svg {
  width: 17px;
  color: #bfc9d4;
}
body.dark .modal-content .modal-header .btn-close {
  background: none;
  box-shadow: none;
  padding: 0;
  margin: 0;
  display: grid;
  opacity: 1;
}
body.dark .modal-content .modal-header .btn-close svg {
  width: 17px;
  height: 17px;
  color: #fff;
}
body.dark .modal-content .modal-body {
  padding: 26px 26px;
}
body.dark .modal-content .modal-body a:not(.btn) {
  color: #4361ee;
  font-weight: 600;
}
body.dark .modal-content .modal-body p {
  color: #888ea8;
  letter-spacing: 1px;
  font-size: 14px;
  line-height: 22px;
  text-align: left;
}
body.dark .modal-content .modal-body p:last-child {
  margin-bottom: 0;
}
body.dark .modal-content .modal-body p:not(:last-child) {
  margin-bottom: 10px;
}
body.dark .modal-content .modal-footer {
  border-top: 1px solid #191e3a;
}
body.dark .modal-content .modal-footer button.btn {
  font-weight: 600;
  padding: 10px 25px;
  letter-spacing: 1px;
}
body.dark .modal-content .modal-footer .btn.btn-primary {
  background-color: #4361ee;
  color: #fff;
  border: 1px solid #4361ee;
}

/*
    Modal Tabs
*/
body.dark .close {
  text-shadow: none;
  color: #bfc9d4;
}
body.dark .close:hover {
  color: #bfc9d4;
}
body.dark .nav-tabs {
  border-bottom: 1px solid #191e3a;
}
body.dark .nav-tabs svg {
  width: 20px;
  vertical-align: bottom;
}
body.dark .nav-tabs .nav-link.active {
  color: #e95f2b;
  background-color: #191e3a;
  border-color: #191e3a #191e3a #0e1726;
}
body.dark .nav-tabs .nav-link.active:after {
  color: #e95f2b;
}
body.dark .nav-tabs .nav-link:hover {
  border-color: #191e3a #191e3a #191e3a;
}

/*
    Modal Success
*/
body.dark .modal-success .modal-content {
  background-color: #0c272b;
}

/*
    Modal Video
*/
body.dark .modal-video .modal-content {
  background-color: transparent;
  border: none;
}
body.dark .modal-video .video-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}
body.dark .modal-video .modal#videoMedia1 .modal-header, body.dark .modal-video .modal#videoMedia2 .modal-header {
  border: none;
  padding: 12px 0;
  justify-content: end;
}
body.dark .modal-video .video-container iframe, body.dark .modal-video .video-container object, body.dark .modal-video .video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
body.dark .modal-video .modal#videoMedia1 .modal-header .close, body.dark .modal-video .modal#videoMedia2 .modal-header .close {
  color: #fff !important;
  opacity: 1;
}
body.dark .modal-video .modal-content .modal-header svg {
  color: #fff;
}

/*
    Modal Notification
*/
body.dark .modal-notification .modal-body .icon-content {
  margin: 0 0 20px 0px;
  display: inline-block;
  padding: 13px;
  border-radius: 50%;
  background: #bfc9d4;
}
body.dark .modal-notification .modal-body .icon-content svg {
  width: 36px;
  height: 36px;
  color: #1b2e4b;
  fill: rgba(0, 23, 55, 0.08);
}

/*
    Profile
*/
body.dark .profile-modal .modal-content {
  background-color: #805dca;
}
body.dark .profile-modal .modal-content .btn-close {
  font-size: 19px;
  font-weight: 600;
  line-height: 1;
  color: #fff;
  text-shadow: none;
  opacity: 1;
  text-align: right;
  background: none;
  margin-left: auto;
  box-shadow: none;
}
body.dark .profile-modal .modal-content .modal-header, body.dark .profile-modal .modal-content .modal-footer {
  border: none;
}
body.dark .profile-modal .modal-content .modal-body p {
  color: #fff;
}
body.dark .profile-modal .modal-content .modal-footer button.btn {
  box-shadow: none;
}
body.dark .modal#sliderModal .modal-content .modal-body button.btn-close {
  position: absolute;
  z-index: 2;
  right: 4px;
  top: -35px;
  opacity: 1;
  text-shadow: none;
  background: transparent;
  box-shadow: none;
}
body.dark .modal#sliderModal .modal-content .modal-body button.btn-close svg {
  color: #bfc9d4;
}
body.dark .modal#sliderModal .modal-content .modal-body button.btn-close:hover svg {
  color: #fff;
}

/*
    Form
*/
.inputForm-modal .modal-content .modal-body .form-group .input-group .input-group-text {
  background: transparent;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  border-right: none;
}
.inputForm-modal .modal-content .modal-body .form-group input {
  border-left: none;
  background: transparent;
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}
.inputForm-modal .modal-content .modal-body .form-group input:focus {
  border-color: #bfc9d4;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
