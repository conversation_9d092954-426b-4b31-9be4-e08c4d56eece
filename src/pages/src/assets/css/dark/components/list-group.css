/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .list-group-item {
  border: 1px solid #1b2e4b;
  padding: 10px 12px;
  background-color: transparent;
  color: #bfc9d4;
  margin-bottom: 0;
}
body.dark .list-group-item .form-check-input:not(:checked) {
  background-color: #515365;
  border-color: #515365;
}
body.dark .list-group-item.active {
  color: #fff;
  background-color: #805dca;
  border-color: transparent;
  box-shadow: 0 1px 15px 1px rgba(52, 40, 104, 0.15);
}
body.dark .list-group-item.active:hover, body.dark .list-group-item.active:focus {
  color: #e0e6ed;
  background-color: #805dca;
  box-shadow: 0px 0px 12px 1px rgba(113, 106, 202, 0.08);
}
body.dark .list-group-item.disabled, body.dark .list-group-item:disabled {
  background: rgba(80, 102, 144, 0.1607843137);
  color: #888ea8;
}
body.dark .new-control-indicator {
  background-color: #f1f2f3;
}
body.dark a.list-group-item.list-group-item-action.active i {
  color: #010156;
}
body.dark code {
  color: #e7515a;
}
body.dark .list-group-item-action:hover {
  color: #e0e6ed;
  background-color: #191e3a;
}
body.dark .list-group-item-action:focus {
  background-color: transparent;
  color: #bfc9d4;
}

/*------list group-----*/
/*
    Icons Meta
*/
body.dark .list-group.list-group-icons-meta .list-group-item.active .media svg {
  font-size: 27px;
  color: #fff;
}
body.dark .list-group.list-group-icons-meta .list-group-item.active .media .media-body h6, body.dark .list-group.list-group-icons-meta .list-group-item.active .media .media-body p {
  color: #fff;
  font-weight: 500;
}
body.dark .list-group.list-group-icons-meta .list-group-item .media svg {
  width: 20px;
  color: #22c7d5;
  height: 20px;
}
body.dark .list-group.list-group-icons-meta .list-group-item .media .media-body h6 {
  color: #888ea8;
  font-weight: 700;
  margin-bottom: 0;
  font-size: 15px;
  letter-spacing: 1px;
}
body.dark .list-group.list-group-icons-meta .list-group-item .media .media-body p {
  color: #888ea8;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
}
body.dark .list-group.list-group-media .list-group-item.active .media .media-body h6, body.dark .list-group.list-group-media .list-group-item.active .media .media-body p {
  color: #fff;
  font-weight: 500;
}
body.dark .list-group.list-group-media .list-group-item .media img {
  color: #4361ee;
  width: 42px;
  height: 42px;
}
body.dark .list-group.list-group-media .list-group-item .media .media-body {
  align-self: center;
}
body.dark .list-group.list-group-media .list-group-item .media .media-body h6 {
  color: #888ea8;
  font-weight: 700;
  margin-bottom: 0;
  font-size: 16px;
  letter-spacing: 1px;
}
body.dark .list-group.list-group-media .list-group-item .media .media-body p {
  color: #888ea8;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
}
body.dark .list-group.task-list-group .list-group-item-action.active {
  background-color: #191e3a;
  color: #fff;
}
body.dark .list-group.task-list-group .list-group-item-action.active .new-control.new-checkbox {
  color: #fff;
  font-size: 14px;
}

/*
    Image Meta
*/
/*
    task-list-group
*/
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
