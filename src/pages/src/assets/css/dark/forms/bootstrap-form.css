/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .row .col-lg-12 .widget .widget-header h4 {
  color: #000000;
  font-size: 20px;
}
body.dark body {
  color: #515365;
  font-weight: 600;
}
body.dark .form-control {
  border: 1px solid #ebedf2;
  border-radius: 4px;
  color: #F49499;
}
body.dark .form-control:disabled, body.dark .form-control[readonly] {
  background-color: #f1f2f3;
}
body.dark .form-control:focus {
  border-color: #805dca;
}
body.dark .help-block, body.dark .help-inline {
  color: #888ea8;
}
body.dark .input-group-addon {
  background-color: #ebedf2;
  color: #3b3f5c;
}
body.dark .dropdown-toggle:after {
  color: #3b3f5c;
}
body.dark .has-warning .control-label, body.dark .has-warning .help-block {
  color: #ffbb44;
}
body.dark .has-warning .form-control {
  border-color: #ffbb44;
}
body.dark .has-error .control-label, body.dark .has-error .help-block {
  color: #ee3d49;
}
body.dark .has-error .form-control {
  border-color: #ee3d49;
}
body.dark .has-success .control-label, body.dark .has-success .help-block {
  color: #009688;
}
body.dark .has-success .form-control {
  border-color: #009688;
}
body.dark .ui-spinner-button {
  border: 1px solid #ebedf2;
  color: #d3d3d3;
}
body.dark .form-horizontal .radio, body.dark .form-horizontal .radio-inline {
  color: #888ea8;
  font-weight: normal;
}
body.dark div.tagsinput {
  border: 1px solid #ebedf2;
}
body.dark div.tagsinput span.tag {
  background: #edf1f7;
  border: 1px solid #ebedf2;
}
body.dark .select2-container .select2-choice {
  border: 1px solid #ebedf2;
  color: #888ea8;
  font-size: 13px;
  font-weight: normal;
}
body.dark .select2-default {
  color: #888ea8 !important;
  font-size: 13px !important;
  font-weight: normal;
}
body.dark .select2-container .select2-choice .select2-arrow {
  border: 1px solid #f1f2f3;
  background: #fff;
}
body.dark .select2-container-multi .select2-choices {
  border: 1px solid #ebedf2;
}
body.dark .select2-container-multi .select2-choices .select2-search-choice {
  border: 1px solid #f1f2f3;
  color: #888ea8;
  font-weight: normal;
  font-size: 13px;
}
body.dark .checkbox, body.dark .radio {
  position: relative;
  display: block;
  cursor: pointer;
}
body.dark .checkbox-inline, body.dark .radio-inline {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}
body.dark .form-horizontal .checkbox, body.dark .form-horizontal .checkbox-inline, body.dark .form-horizontal .radio, body.dark .form-horizontal .radio-inline {
  padding-top: 7px;
  margin-top: 0;
  margin-bottom: 0;
}
body.dark .form-horizontal .checkbox, body.dark .form-horizontal .radio {
  min-height: 27px;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
