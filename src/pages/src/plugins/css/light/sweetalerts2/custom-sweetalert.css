/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.swal2-popup {
  background-color: #fff !important;
}

.swal2-title {
  color: #3b3f5c;
}

.swal2-html-container {
  color: #e95f2b;
}

.swal2-styled.swal2-default-outline:focus, .swal2-styled.swal2-confirm:focus {
  box-shadow: none;
}

.swal2-icon.swal2-success .swal2-success-ring {
  border-color: #ddf5f0;
}
.swal2-icon.swal2-success [class^=swal2-success-line] {
  background-color: #00ab55;
}
.swal2-icon.swal2-error {
  border-color: #fbeced;
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
  background-color: #e7515a;
}
.swal2-icon.swal2-warning {
  border-color: #fcf5e9;
  color: #e2a03f;
}
.swal2-icon.swal2-info {
  border-color: #e6f4ff;
  color: #2196f3;
}
.swal2-icon.swal2-question {
  border-color: #f2eafa;
  color: #805dca;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
