/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .custom-progress.progress-up .range-count {
  margin-bottom: 15px;
}
body.dark .custom-progress.progress-down .range-count {
  margin-top: 15px;
}
body.dark .range-count {
  font-weight: 700;
  color: #3b3f5c;
}
body.dark .range-count .range-count-number {
  display: inline-block;
  background: #1b2e4b;
  padding: 3px 8px;
  border-radius: 5px;
  color: #009688;
  border: 1px solid #1b2e4b;
}
body.dark .range-count .range-count-unit {
  color: #009688;
}
body.dark .custom-progress.top-right .range-count, body.dark .custom-progress.bottom-right .range-count {
  text-align: right;
}
body.dark .progress-range-counter::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #009688;
  cursor: pointer;
  height: 16px;
  width: 16px;
  margin-top: -4px;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
body.dark .progress-range-counter:active::-webkit-slider-thumb {
  transform: scale(1.2);
}
body.dark .progress-range-counter:focus::-webkit-slider-thumb {
  background: #009688;
  cursor: pointer;
  height: 16px;
  width: 16px;
  margin-top: -4px;
  box-shadow: none;
}
body.dark .progress-range-counter::-moz-range-thumb {
  background: #009688;
  cursor: pointer;
  height: 16px;
  width: 16px;
  margin-top: -4px;
}
body.dark input[type=range]::-webkit-slider-runnable-track, body.dark input[type=range]::-moz-range-track, body.dark input[type=range]::-ms-track {
  background: #191e3a;
}/*# sourceMappingURL=bootstrap-slider.css.map */