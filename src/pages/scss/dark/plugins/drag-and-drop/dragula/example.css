/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark pre {
  white-space: pre-wrap;
}
body.dark button.btn.btn-button-16.btn-sm {
  padding: 7px 30px;
  font-size: 13px;
}
body.dark sub {
  display: block;
  text-align: right;
  margin-top: -10px;
  font-size: 11px;
  font-style: italic;
}
body.dark ul {
  margin: 0;
  padding: 0;
}
body.dark .header-search > form > .input-box > .search-box {
  background-color: #77EDB0;
  border: none;
  line-height: 25px;
  border-radius: 4px;
  color: #060818;
  margin: 0px 0;
  display: inline;
  width: auto;
}

/*
 * note that styling gu-mirror directly is a bad practice because it's too generic.
 * you're better off giving the draggable elements a unique class and styling that directly!
 */
body.dark .dragula > div, body.dark .gu-mirror {
  margin: 10px;
  padding: 10px;
  transition: opacity 0.4s ease-in-out;
}
body.dark .dragula > div {
  cursor: move;
  cursor: grab;
  cursor: -webkit-grab;
}
body.dark .gu-mirror {
  cursor: grabbing;
  cursor: -webkit-grabbing;
}
body.dark .dragula .ex-moved {
  background-color: #e74c3c;
}
body.dark #left-lovehandles > div, body.dark #right-lovehandles > div {
  cursor: initial;
}
body.dark .image-thing {
  margin: 20px 0;
  display: block;
  text-align: center;
}
body.dark .slack-join {
  position: absolute;
  font-weight: normal;
  font-size: 14px;
  right: 10px;
  top: 50%;
  margin-top: -8px;
  line-height: 16px;
}
body.dark .parent.ex-1 .dragula {
  padding: 15px;
}
body.dark .parent.ex-1 .dragula .media {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
}
body.dark body.gu-unselectable .media.el-drag-ex-1 {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
}
body.dark .parent.ex-1 .dragula .media img, body.dark body.gu-unselectable .media.el-drag-ex-1 img {
  width: 45px;
  border-radius: 50%;
  margin-right: 17px;
  height: 45px;
}
body.dark .parent.ex-1 .dragula .media .media-body, body.dark body.gu-unselectable .media.el-drag-ex-1 .media-body {
  align-self: center;
}
body.dark .parent.ex-1 .dragula .media .media-body h6, body.dark body.gu-unselectable .media.el-drag-ex-1 .media-body h6 {
  color: #bfc9d4;
  font-weight: 600;
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 0;
}
body.dark .parent.ex-1 .dragula .media .media-body p, body.dark body.gu-unselectable .media.el-drag-ex-1 .media-body p {
  color: #888ea8;
  font-weight: 600;
  margin-bottom: 0;
  font-size: 12px;
}
body.dark .parent.ex-2 .dragula {
  padding: 15px;
}
body.dark .parent.ex-2 .dragula .media {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
}
body.dark body.gu-unselectable .media.el-drag-ex-2 {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
}
body.dark .parent.ex-2 .dragula .media img, body.dark body.gu-unselectable .media.el-drag-ex-2 img {
  width: 45px;
  border-radius: 50%;
  margin-right: 17px;
  height: 45px;
}
body.dark .parent.ex-2 .dragula .media i, body.dark body.gu-unselectable .media.el-drag-ex-2 i {
  font-size: 19px;
  border-radius: 20px;
}
body.dark .parent.ex-2 .dragula .media .media-body, body.dark body.gu-unselectable .media.el-drag-ex-2 .media-body {
  align-self: center;
}
body.dark .parent.ex-2 .dragula .media .media-body h6, body.dark body.gu-unselectable .media.el-drag-ex-2 .media-body h6 {
  color: #bfc9d4;
  font-weight: 600;
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 0;
}
body.dark .parent.ex-2 .dragula .media .media-body p, body.dark body.gu-unselectable .media.el-drag-ex-2 .media-body p {
  color: #888ea8;
  font-weight: 600;
  margin-bottom: 0;
  font-size: 12px;
}
body.dark .parent.ex-2 #left-events .f-icon-fill, body.dark body.gu-unselectable .media.el-drag-ex-2 .f-icon-fill {
  display: none !important;
  color: #e7515a;
  fill: rgba(231, 81, 90, 0.4196078431);
}
body.dark .parent.ex-2 #left-events .f-icon-line, body.dark body.gu-unselectable .media.el-drag-ex-2 .f-icon-line {
  display: block !important;
  color: #e2a03f;
  width: 17px;
  fill: rgba(226, 160, 63, 0.4196078431);
}
body.dark .parent.ex-2 #right-events .f-icon-fill, body.dark body.gu-unselectable .media.el-drag-ex-2 .f-icon-fill {
  display: block !important;
  color: #e7515a;
  fill: rgba(231, 81, 90, 0.4196078431);
  display: block !important;
  width: 17px;
}
body.dark .parent.ex-2 #right-events .f-icon-line, body.dark body.gu-unselectable .media.el-drag-ex-2 .f-icon-line {
  display: none !important;
  color: #e7515a;
  fill: rgba(231, 81, 90, 0.4196078431);
}
body.dark .parent.ex-3 .dragula {
  background-color: transparent;
  padding: 15px;
}
body.dark .parent.ex-3 .dragula div {
  padding: 0;
  margin: 0;
}
body.dark .parent.ex-3 .dragula div.media {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
  margin-bottom: 10px;
}
body.dark body.gu-unselectable div.media.el-drag-ex-3.gu-mirror {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
  margin-bottom: 10px;
}
body.dark .parent.ex-3 .dragula .media img, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror img {
  width: 45px;
  border-radius: 10%;
  margin-right: 17px;
  height: 45px;
}
body.dark .parent.ex-3 .dragula .media .media-body, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror .media-body {
  align-self: center;
}
body.dark .parent.ex-3 .dragula .media .media-body h5, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror .media-body h5 {
  color: #bfc9d4;
  font-weight: 600;
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 0;
}
body.dark .parent.ex-3 .dragula .media .media-body h5 span.usr-commented, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror .media-body h5 span.usr-commented {
  font-weight: 600;
  color: #bfc9d4;
  font-size: 14px;
}
body.dark .parent.ex-3 .dragula .media .media-body h5 span.comment-topic, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror .media-body h5 span.comment-topic {
  font-weight: 600;
  color: #2196f3;
  font-size: 13px;
}
body.dark .parent.ex-3 .dragula .media .media-body p.meta-time, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror .media-body p.meta-time {
  color: #888ea8;
  font-weight: 600;
  margin-bottom: 0;
  font-size: 12px;
}
body.dark .parent.ex-4 .card.post .media.user-meta, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror .media.user-meta {
  padding: 10px;
}
body.dark .parent.ex-4 .card.post .media.user-meta img, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror .media.user-meta img {
  width: 45px;
  border-radius: 10%;
  margin-right: 17px;
  height: 45px;
}
body.dark .parent.ex-4 .card.post .media.user-meta .media-body, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror .media.user-meta .media-body {
  align-self: center;
}
body.dark .parent.ex-4 .card.post .media.user-meta .media-body h5, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror .media.user-meta .media-body h5 {
  color: #bfc9d4;
  font-weight: 600;
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 0;
}
body.dark .parent.ex-4 .card.post .media.user-meta .media-body p, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror .media.user-meta .media-body p {
  color: #888ea8;
  font-weight: 600;
  margin-bottom: 0;
  font-size: 12px;
}
body.dark .parent.ex-4 .card.post.text-post .card-body .post-content, body.dark body.gu-unselectable .card.post.text-post.el-drag-ex-4.gu-mirror .card-body .post-content {
  padding: 20px 18px;
  color: #888ea8 !important;
  border-bottom: 1px solid #3b3f5c;
  margin-bottom: 15px;
}
body.dark .parent.ex-4 .card.post.text-post .card-body .post-content p, body.dark body.gu-unselectable .card.post.text-post.el-drag-ex-4.gu-mirror .card-body .post-content p {
  color: #888ea8 !important;
}
body.dark .parent.ex-4 .card.post div.people-liked-post ul, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror div.people-liked-post ul {
  padding-left: 23px;
}
body.dark .parent.ex-4 .card.post div.people-liked-post ul li img, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror div.people-liked-post ul li img {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid rgba(59, 63, 92, 0.25);
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.3);
  margin-left: -21px;
}
body.dark .parent.ex-4 .card.post div.people-liked-post .people-liked-post-name span, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror div.people-liked-post .people-liked-post-name span {
  vertical-align: -webkit-baseline-middle;
  font-size: 12px;
}
body.dark .parent.ex-4 .card.post div.people-liked-post .people-liked-post-name span a, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror div.people-liked-post .people-liked-post-name span a {
  color: #2196f3;
  font-weight: 600;
  font-size: 13px;
}
body.dark .card.post.text-post {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
}
body.dark .card.post.text-post .card-body {
  padding: 0;
}

/*Ex -5*/
body.dark .parent.ex-5 .dragula div, body.dark .parent.ex-5 .dragula .gu-transit {
  color: #fff;
  align-self: center;
}
body.dark .parent.ex-5 .dragula > div, body.dark .parent.ex-5 .dragula > .gu-transit {
  background-color: #1b2e4b;
  border-radius: 6px;
  border: 1px solid #3b3f5c;
  padding: 14px 26px;
}
body.dark .parent.ex-5 .handle {
  padding: 0 9px;
  margin-right: 5px;
  background-color: #0e1726;
  border-radius: 2px;
  color: #fff;
  cursor: move;
}
body.dark body.gu-unselectable .handle {
  padding: 0 9px;
  margin-right: 5px;
  background-color: #0e1726;
  border-radius: 2px;
  color: #fff;
  cursor: move;
}
body.dark .parent.ex-5 .media ul, body.dark body.gu-unselectable .media.el-drag-ex-5.gu-mirror ul {
  position: relative;
  margin-right: 17px;
}
body.dark .parent.ex-5 .media ul li.badge-notify, body.dark body.gu-unselectable .media.el-drag-ex-5.gu-mirror ul li.badge-notify {
  position: relative;
}
body.dark .parent.ex-5 .media ul li .notification, body.dark body.gu-unselectable .media.el-drag-ex-5.gu-mirror ul li .notification {
  position: absolute;
  top: -30px;
  left: 0;
}
body.dark .parent.ex-5 .media ul li .notification span.badge, body.dark body.gu-unselectable .media.el-drag-ex-5.gu-mirror ul li .notification span.badge {
  border-radius: 50px;
  padding: 2px 6px;
}
body.dark .parent.ex-5 .media ul li img, body.dark body.gu-unselectable .media.el-drag-ex-5.gu-mirror ul li img {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid rgba(59, 63, 92, 0.25);
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  margin-left: -26px;
}
body.dark .parent.ex-5 .dragula .media .media-body h5, body.dark body.gu-unselectable .media.el-drag-ex-5.gu-mirror .media-body h6 {
  color: #000;
}
body.dark .parent.ex-5 .dragula .media .media-body h5, body.dark .parent.ex-5 .dragula .gu-transit .media.el-drag-ex-5.gu-mirror .media-body h5 {
  font-weight: 600;
  color: #bfc9d4;
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 0;
}
body.dark .parent.ex-5 .dragula .media .media-body p, body.dark .parent.ex-5 .dragula .gu-transit .media .media-body p {
  color: #000;
}
@media screen and (max-width: 1199px) {
  body.dark .parent.ex-1 .dragula .media .media-body button, body.dark body.gu-unselectable .media.el-drag-ex-1 .media-body button {
    margin-top: 15px;
  }
}
@media screen and (max-width: 768px) {
  body.dark .parent.ex-1 .dragula .media img, body.dark body.gu-unselectable .media.el-drag-ex-1 img {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
@media screen and (max-width: 575px) {
  body.dark .parent.ex-2 .dragula .media img, body.dark body.gu-unselectable .media.el-drag-ex-2 img, body.dark .parent.ex-3 .dragula .media img, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror img {
    margin-bottom: 15px;
    margin-right: 0;
  }
  body.dark .parent.ex-3 .dragula .media .media-body p.meta-time, body.dark body.gu-unselectable .media.el-drag-ex-3.gu-mirror .media-body p.meta-time {
    margin-top: 5px;
  }
  body.dark .card.post.text-post {
    padding: 14px 5px;
  }
  body.dark .parent.ex-4 .card.post .media.user-meta img, body.dark body.gu-unselectable .card.post.el-drag-ex-4.gu-mirror .media.user-meta img {
    margin-bottom: 15px;
    margin-right: 0;
  }
  body.dark .parent.ex-5 .media ul {
    margin-bottom: 15px;
    margin-right: 0;
  }
  body.dark body.gu-unselectable .media.el-drag-ex-5.gu-mirror ul {
    margin-bottom: 15px;
    margin-right: 0;
  }
  body.dark .parent.ex-5 .handle, body.dark body.gu-unselectable .handle {
    display: inline-block;
    margin-top: 15px;
    margin-right: 0;
  }
}
@media screen and (max-width: 991px) {
  body.dark .parent {
    margin: 12px 0;
    padding: 5px;
  }
}/*# sourceMappingURL=example.css.map */