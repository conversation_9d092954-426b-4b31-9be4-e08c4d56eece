@charset "UTF-8";
/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/**
 * simplemde v1.11.2
 * Copyright Next Step Webs, Inc.
 * @link https://github.com/NextStepWebs/simplemde-markdown-editor
 * @license MIT
 */
body.dark .CodeMirror {
  color: #bfc9d4;
}
body.dark .CodeMirror-lines {
  padding: 4px 0;
}
body.dark .CodeMirror pre {
  padding: 0 4px;
}
body.dark .CodeMirror-gutter-filler, body.dark .CodeMirror-scrollbar-filler {
  background-color: #fff;
}
body.dark .CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f1f2f3;
  white-space: nowrap;
}
body.dark .CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #888ea8;
  white-space: nowrap;
}
body.dark .CodeMirror-guttermarker {
  color: #000;
}
body.dark .CodeMirror-guttermarker-subtle {
  color: #888ea8;
}
body.dark .CodeMirror-cursor {
  border-left: 1px solid #fafafa;
  border-right: none;
  width: 0;
}
body.dark .CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}
body.dark .cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}
body.dark .cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}
body.dark .cm-animate-fat-cursor {
  width: auto;
  border: 0;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}
@keyframes blink {
  50% {
    background-color: transparent;
  }
}
body.dark .cm-tab {
  display: inline-block;
  text-decoration: inherit;
}
body.dark .CodeMirror-ruler {
  border-left: 1px solid #ccc;
  position: absolute;
}
body.dark .cm-s-default .cm-header {
  color: #00f;
}
body.dark .cm-s-default .cm-quote {
  color: #090;
}
body.dark .cm-negative {
  color: #d44;
}
body.dark .cm-positive {
  color: #292;
}
body.dark .cm-header, body.dark .cm-strong {
  font-weight: 700;
}
body.dark .cm-em {
  font-style: italic;
}
body.dark .cm-link {
  text-decoration: underline;
}
body.dark .cm-strikethrough {
  text-decoration: line-through;
}
body.dark .cm-s-default .cm-keyword {
  color: #708;
}
body.dark .cm-s-default .cm-atom {
  color: #219;
}
body.dark .cm-s-default .cm-number {
  color: #164;
}
body.dark .cm-s-default .cm-def {
  color: #00f;
}
body.dark .cm-s-default .cm-variable-2 {
  color: #05a;
}
body.dark .cm-s-default .cm-variable-3 {
  color: #085;
}
body.dark .cm-s-default .cm-comment {
  color: #a50;
}
body.dark .cm-s-default .cm-string {
  color: #a11;
}
body.dark .cm-s-default .cm-string-2 {
  color: #f50;
}
body.dark .cm-s-default .cm-meta, body.dark .cm-s-default .cm-qualifier {
  color: #555;
}
body.dark .cm-s-default .cm-builtin {
  color: #30a;
}
body.dark .cm-s-default .cm-bracket {
  color: #997;
}
body.dark .cm-s-default .cm-tag {
  color: #170;
}
body.dark .cm-s-default .cm-attribute {
  color: #00c;
}
body.dark .cm-s-default .cm-hr {
  color: #888ea8;
}
body.dark .cm-s-default .cm-link {
  color: #00c;
}
body.dark .cm-invalidchar, body.dark .cm-s-default .cm-error {
  color: red;
}
body.dark .CodeMirror-composing {
  border-bottom: 2px solid;
}
body.dark div.CodeMirror span.CodeMirror-matchingbracket {
  color: #0f0;
}
body.dark div.CodeMirror span.CodeMirror-nonmatchingbracket {
  color: #f22;
}
body.dark .CodeMirror-matchingtag {
  background: rgba(255, 150, 0, 0.3);
}
body.dark .CodeMirror-activeline-background {
  background: #e8f2ff;
}
body.dark .CodeMirror {
  position: relative;
  overflow: hidden;
  background: transparent;
}
body.dark .CodeMirror-scroll {
  overflow: scroll !important;
  margin-bottom: -30px;
  margin-right: -30px;
  padding-bottom: 30px;
  height: 100%;
  outline: 0;
  position: relative;
}
body.dark .CodeMirror-sizer {
  position: relative;
  border-right: 30px solid transparent;
}
body.dark .CodeMirror-gutter-filler, body.dark .CodeMirror-hscrollbar, body.dark .CodeMirror-scrollbar-filler {
  position: absolute;
  z-index: 6;
  display: none;
}
body.dark .CodeMirror-vscrollbar {
  position: absolute;
  z-index: 6;
  display: none;
  right: 0;
  top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}
body.dark .CodeMirror-hscrollbar {
  bottom: 0;
  left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}
body.dark .CodeMirror-scrollbar-filler {
  right: 0;
  bottom: 0;
}
body.dark .CodeMirror-gutter-filler {
  left: 0;
  bottom: 0;
}
body.dark .CodeMirror-gutters {
  position: absolute;
  left: 0;
  top: 0;
  min-height: 100%;
  z-index: 3;
}
body.dark .CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -30px;
}
body.dark .CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: 0 0 !important;
  border: none !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
body.dark .CodeMirror-gutter-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 4;
}
body.dark .CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}
body.dark .CodeMirror-lines {
  cursor: text;
  min-height: 1px;
}
body.dark .CodeMirror pre {
  border-radius: 0;
  border-width: 0;
  background: 0 0;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  font-variant-ligatures: none;
}
body.dark .CodeMirror-wrap pre {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}
body.dark .CodeMirror-linebackground {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
}
body.dark .CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  overflow: auto;
}
body.dark .CodeMirror-code {
  outline: 0;
}
body.dark .CodeMirror-gutter, body.dark .CodeMirror-gutters, body.dark .CodeMirror-linenumber, body.dark .CodeMirror-scroll, body.dark .CodeMirror-sizer {
  box-sizing: content-box;
}
body.dark .CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
body.dark .CodeMirror-cursor {
  position: absolute;
}
body.dark .CodeMirror-measure pre {
  position: static;
}
body.dark div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}
body.dark .CodeMirror-focused div.CodeMirror-cursors, body.dark div.CodeMirror-dragcursors {
  visibility: visible;
}
body.dark .CodeMirror-selected {
  background: #d9d9d9;
}
body.dark .CodeMirror-focused .CodeMirror-selected {
  background: #d7d4f0;
}
body.dark .CodeMirror-line::-moz-selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line::selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line > span::-moz-selection, body.dark .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line > span::selection, body.dark .CodeMirror-line > span > span::selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-crosshair {
  cursor: crosshair;
}
body.dark .CodeMirror-line::-moz-selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line > span::-moz-selection, body.dark .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}
body.dark .cm-searching {
  background: #ffa;
  background: rgba(255, 255, 0, 0.4);
}
body.dark .cm-force-border {
  padding-right: 0.1px;
}
@media print {
  body.dark .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}
body.dark .cm-tab-wrap-hack:after {
  content: "";
}
body.dark span.CodeMirror-selectedtext {
  background: 0 0;
}
body.dark .CodeMirror {
  height: auto;
  min-height: 300px;
  border: none;
  border-radius: 6px;
  padding: 10px;
  font: inherit;
  z-index: 1;
  border: 1px solid #1b2e4b;
  margin-top: 28px;
}
body.dark .CodeMirror-scroll {
  min-height: 300px;
}
body.dark .CodeMirror-fullscreen {
  background: #fff;
  position: fixed !important;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  height: auto;
  z-index: 9;
}
body.dark .CodeMirror-sided {
  width: 50% !important;
}
body.dark .editor-toolbar {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  padding: 0 10px;
  border-top: 1px solid #1b2e4b;
  border-bottom: 1px solid #1b2e4b;
  border-left: 1px solid #1b2e4b;
  border-right: 1px solid #1b2e4b;
  border-radius: 6px;
}
body.dark .editor-toolbar:after {
  display: block;
  content: " ";
  height: 1px;
}
body.dark .editor-toolbar:before {
  display: block;
  content: " ";
  height: 1px;
  margin-bottom: 8px;
}
body.dark .editor-toolbar:after {
  margin-top: 8px;
}
body.dark .editor-toolbar:hover {
  opacity: 0.8;
}
body.dark .editor-wrapper input.title:focus, body.dark .editor-wrapper input.title:hover {
  opacity: 0.8;
}
body.dark .editor-toolbar.fullscreen {
  width: 100%;
  height: 50px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding-top: 10px;
  padding-bottom: 10px;
  box-sizing: border-box;
  background: #fff;
  border: 0;
  position: fixed;
  top: 0;
  left: 0;
  opacity: 1;
  z-index: 9;
}
body.dark .editor-toolbar.fullscreen::before {
  width: 20px;
  height: 50px;
  background: linear-gradient(to right, rgb(255, 255, 255) 0, rgba(255, 255, 255, 0) 100%);
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}
body.dark .editor-toolbar.fullscreen::after {
  width: 20px;
  height: 50px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0, rgb(255, 255, 255) 100%);
  position: fixed;
  top: 0;
  right: 0;
  margin: 0;
  padding: 0;
}
body.dark .editor-toolbar a {
  display: inline-block;
  text-align: center;
  text-decoration: none !important;
  color: #009688 !important;
  width: 30px;
  height: 30px;
  margin: 0 0 0 2px;
  border: 1px solid transparent;
  border-radius: 3px;
  cursor: pointer;
}
body.dark .editor-toolbar a.active, body.dark .editor-toolbar a:hover {
  background: #191e3a;
  border-color: #191e3a;
}
body.dark .editor-toolbar a:before {
  line-height: 30px;
}
body.dark .editor-toolbar i.separator {
  display: inline-block;
  width: 0;
  border-left: 1px solid #1b2e4b;
  border-right: 1px solid #1b2e4b;
  color: transparent;
  text-indent: -10px;
  margin: 0 6px;
}
body.dark .editor-toolbar a.fa-header-x:after {
  font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 65%;
  vertical-align: text-bottom;
  position: relative;
  top: 2px;
}
body.dark .editor-toolbar a.fa-header-1:after {
  content: "1";
}
body.dark .editor-toolbar a.fa-header-2:after {
  content: "2";
}
body.dark .editor-toolbar a.fa-header-3:after {
  content: "3";
}
body.dark .editor-toolbar a.fa-header-bigger:after {
  content: "▲";
}
body.dark .editor-toolbar a.fa-header-smaller:after {
  content: "▼";
}
body.dark .editor-toolbar.disabled-for-preview a:not(.no-disable) {
  pointer-events: none;
  background: #060818;
  border-color: #060818;
  text-shadow: inherit;
}
@media only screen and (max-width: 700px) {
  body.dark .editor-toolbar a.no-mobile {
    display: none;
  }
}
body.dark .editor-statusbar {
  padding: 8px 10px;
  font-size: 12px;
  color: #888ea8;
  text-align: right;
}
body.dark .editor-statusbar span {
  display: inline-block;
  min-width: 4em;
  margin-left: 1em;
}
body.dark .editor-preview, body.dark .editor-preview-side {
  padding: 10px;
  background: #0e1726;
  overflow: auto;
  display: none;
  box-sizing: border-box;
}
body.dark .editor-statusbar .lines:before {
  content: "lines: ";
}
body.dark .editor-statusbar .words:before {
  content: "words: ";
}
body.dark .editor-statusbar .characters:before {
  content: "characters: ";
}
body.dark .editor-preview {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 7;
}
body.dark .editor-preview-side {
  position: fixed;
  bottom: 0;
  width: 50%;
  top: 50px;
  right: 0;
  z-index: 9;
  border: 1px solid #ddd;
}
body.dark .editor-preview-active, body.dark .editor-preview-active-side {
  display: block;
}
body.dark .editor-preview-side > p {
  margin-top: 0;
}
body.dark .editor-preview > p {
  margin-top: 0;
}
body.dark .editor-preview pre {
  background: #eee;
  margin-bottom: 10px;
}
body.dark .editor-preview-side pre {
  background: #eee;
  margin-bottom: 10px;
}
body.dark .editor-preview table td, body.dark .editor-preview table th {
  border: 1px solid #ddd;
  padding: 5px;
}
body.dark .editor-preview-side table td, body.dark .editor-preview-side table th {
  border: 1px solid #ddd;
  padding: 5px;
}
body.dark .CodeMirror .CodeMirror-code .cm-tag {
  color: #63a35c;
}
body.dark .CodeMirror .CodeMirror-code .cm-attribute {
  color: #795da3;
}
body.dark .CodeMirror .CodeMirror-code .cm-string {
  color: #183691;
}
body.dark .CodeMirror .CodeMirror-selected {
  background: #d9d9d9;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-1 {
  font-size: 200%;
  line-height: 200%;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-2 {
  font-size: 160%;
  line-height: 160%;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-3 {
  font-size: 125%;
  line-height: 125%;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-4 {
  font-size: 110%;
  line-height: 110%;
}
body.dark .CodeMirror .CodeMirror-code .cm-comment {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}
body.dark .CodeMirror .CodeMirror-code .cm-link {
  color: #7f8c8d;
}
body.dark .CodeMirror .CodeMirror-code .cm-url {
  color: #aab2b3;
}
body.dark .CodeMirror .CodeMirror-code .cm-strikethrough {
  text-decoration: line-through;
}
body.dark .CodeMirror .CodeMirror-placeholder {
  opacity: 0.5;
}
body.dark .CodeMirror .cm-spell-error:not(.cm-url):not(.cm-comment):not(.cm-tag):not(.cm-word) {
  background: rgba(255, 0, 0, 0.15);
}
body.dark .CodeMirror {
  color: #bfc9d4;
}
body.dark .CodeMirror-lines {
  padding: 4px 0;
}
body.dark .CodeMirror pre {
  padding: 0 4px;
}
body.dark .CodeMirror-gutter-filler, body.dark .CodeMirror-scrollbar-filler {
  background-color: #fff;
}
body.dark .CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f1f2f3;
  white-space: nowrap;
}
body.dark .CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #888ea8;
  white-space: nowrap;
}
body.dark .CodeMirror-guttermarker {
  color: #000;
}
body.dark .CodeMirror-guttermarker-subtle {
  color: #888ea8;
}
body.dark .CodeMirror-cursor {
  border-left: 1px solid #fafafa;
  border-right: none;
  width: 0;
}
body.dark .CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}
body.dark .cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}
body.dark .cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}
body.dark .cm-animate-fat-cursor {
  width: auto;
  border: 0;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}
@keyframes blink {
  50% {
    background-color: transparent;
  }
}
body.dark .cm-tab {
  display: inline-block;
  text-decoration: inherit;
}
body.dark .CodeMirror-ruler {
  border-left: 1px solid #ccc;
  position: absolute;
}
body.dark .cm-s-default .cm-header {
  color: #00f;
}
body.dark .cm-s-default .cm-quote {
  color: #090;
}
body.dark .cm-negative {
  color: #d44;
}
body.dark .cm-positive {
  color: #292;
}
body.dark .cm-header, body.dark .cm-strong {
  font-weight: 700;
}
body.dark .cm-em {
  font-style: italic;
}
body.dark .cm-link {
  text-decoration: underline;
}
body.dark .cm-strikethrough {
  text-decoration: line-through;
}
body.dark .cm-s-default .cm-keyword {
  color: #708;
}
body.dark .cm-s-default .cm-atom {
  color: #219;
}
body.dark .cm-s-default .cm-number {
  color: #164;
}
body.dark .cm-s-default .cm-def {
  color: #00f;
}
body.dark .cm-s-default .cm-variable-2 {
  color: #05a;
}
body.dark .cm-s-default .cm-variable-3 {
  color: #085;
}
body.dark .cm-s-default .cm-comment {
  color: #a50;
}
body.dark .cm-s-default .cm-string {
  color: #a11;
}
body.dark .cm-s-default .cm-string-2 {
  color: #f50;
}
body.dark .cm-s-default .cm-meta, body.dark .cm-s-default .cm-qualifier {
  color: #555;
}
body.dark .cm-s-default .cm-builtin {
  color: #30a;
}
body.dark .cm-s-default .cm-bracket {
  color: #997;
}
body.dark .cm-s-default .cm-tag {
  color: #170;
}
body.dark .cm-s-default .cm-attribute {
  color: #00c;
}
body.dark .cm-s-default .cm-hr {
  color: #888ea8;
}
body.dark .cm-s-default .cm-link {
  color: #00c;
}
body.dark .cm-invalidchar, body.dark .cm-s-default .cm-error {
  color: red;
}
body.dark .CodeMirror-composing {
  border-bottom: 2px solid;
}
body.dark div.CodeMirror span.CodeMirror-matchingbracket {
  color: #0f0;
}
body.dark div.CodeMirror span.CodeMirror-nonmatchingbracket {
  color: #f22;
}
body.dark .CodeMirror-matchingtag {
  background: rgba(255, 150, 0, 0.3);
}
body.dark .CodeMirror-activeline-background {
  background: #e8f2ff;
}
body.dark .CodeMirror {
  position: relative;
  overflow: hidden;
  background: transparent;
}
body.dark .CodeMirror-scroll {
  overflow: scroll !important;
  margin-bottom: -30px;
  margin-right: -30px;
  padding-bottom: 30px;
  height: 100%;
  outline: 0;
  position: relative;
}
body.dark .CodeMirror-sizer {
  position: relative;
  border-right: 30px solid transparent;
}
body.dark .CodeMirror-gutter-filler, body.dark .CodeMirror-hscrollbar, body.dark .CodeMirror-scrollbar-filler {
  position: absolute;
  z-index: 6;
  display: none;
}
body.dark .CodeMirror-vscrollbar {
  position: absolute;
  z-index: 6;
  display: none;
  right: 0;
  top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}
body.dark .CodeMirror-hscrollbar {
  bottom: 0;
  left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}
body.dark .CodeMirror-scrollbar-filler {
  right: 0;
  bottom: 0;
}
body.dark .CodeMirror-gutter-filler {
  left: 0;
  bottom: 0;
}
body.dark .CodeMirror-gutters {
  position: absolute;
  left: 0;
  top: 0;
  min-height: 100%;
  z-index: 3;
}
body.dark .CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -30px;
}
body.dark .CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: 0 0 !important;
  border: none !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
body.dark .CodeMirror-gutter-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 4;
}
body.dark .CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}
body.dark .CodeMirror-lines {
  cursor: text;
  min-height: 1px;
}
body.dark .CodeMirror pre {
  border-radius: 0;
  border-width: 0;
  background: 0 0;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  font-variant-ligatures: none;
}
body.dark .CodeMirror-wrap pre {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}
body.dark .CodeMirror-linebackground {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
}
body.dark .CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  overflow: auto;
}
body.dark .CodeMirror-code {
  outline: 0;
}
body.dark .CodeMirror-gutter, body.dark .CodeMirror-gutters, body.dark .CodeMirror-linenumber, body.dark .CodeMirror-scroll, body.dark .CodeMirror-sizer {
  box-sizing: content-box;
}
body.dark .CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
body.dark .CodeMirror-cursor {
  position: absolute;
}
body.dark .CodeMirror-measure pre {
  position: static;
}
body.dark div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}
body.dark .CodeMirror-focused div.CodeMirror-cursors, body.dark div.CodeMirror-dragcursors {
  visibility: visible;
}
body.dark .CodeMirror-selected {
  background: #d9d9d9;
}
body.dark .CodeMirror-focused .CodeMirror-selected {
  background: #d7d4f0;
}
body.dark .CodeMirror-line::-moz-selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line::selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line > span::-moz-selection, body.dark .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line > span::selection, body.dark .CodeMirror-line > span > span::selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-crosshair {
  cursor: crosshair;
}
body.dark .CodeMirror-line::-moz-selection {
  background: #d7d4f0;
}
body.dark .CodeMirror-line > span::-moz-selection, body.dark .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}
body.dark .cm-searching {
  background: #ffa;
  background: rgba(255, 255, 0, 0.4);
}
body.dark .cm-force-border {
  padding-right: 0.1px;
}
@media print {
  body.dark .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}
body.dark .cm-tab-wrap-hack:after {
  content: "";
}
body.dark span.CodeMirror-selectedtext {
  background: 0 0;
}
body.dark .CodeMirror {
  height: auto;
  min-height: 300px;
  border: none;
  border-radius: 6px;
  padding: 10px;
  font: inherit;
  z-index: 1;
  border: 1px solid #1b2e4b;
  margin-top: 28px;
}
body.dark .CodeMirror-scroll {
  min-height: 300px;
}
body.dark .CodeMirror-fullscreen {
  background: #fff;
  position: fixed !important;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  height: auto;
  z-index: 9;
}
body.dark .CodeMirror-sided {
  width: 50% !important;
}
body.dark .editor-toolbar {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  padding: 0 10px;
  border-top: 1px solid #1b2e4b;
  border-bottom: 1px solid #1b2e4b;
  border-left: 1px solid #1b2e4b;
  border-right: 1px solid #1b2e4b;
  border-radius: 6px;
}
body.dark .editor-toolbar:after {
  display: block;
  content: " ";
  height: 1px;
}
body.dark .editor-toolbar:before {
  display: block;
  content: " ";
  height: 1px;
  margin-bottom: 8px;
}
body.dark .editor-toolbar:after {
  margin-top: 8px;
}
body.dark .editor-toolbar:hover {
  opacity: 0.8;
}
body.dark .editor-wrapper input.title:focus, body.dark .editor-wrapper input.title:hover {
  opacity: 0.8;
}
body.dark .editor-toolbar.fullscreen {
  width: 100%;
  height: 50px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding-top: 10px;
  padding-bottom: 10px;
  box-sizing: border-box;
  background: #fff;
  border: 0;
  position: fixed;
  top: 0;
  left: 0;
  opacity: 1;
  z-index: 9;
}
body.dark .editor-toolbar.fullscreen::before {
  width: 20px;
  height: 50px;
  background: linear-gradient(to right, rgb(255, 255, 255) 0, rgba(255, 255, 255, 0) 100%);
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}
body.dark .editor-toolbar.fullscreen::after {
  width: 20px;
  height: 50px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0, rgb(255, 255, 255) 100%);
  position: fixed;
  top: 0;
  right: 0;
  margin: 0;
  padding: 0;
}
body.dark .editor-toolbar a {
  display: inline-block;
  text-align: center;
  text-decoration: none !important;
  color: #009688 !important;
  width: 30px;
  height: 30px;
  margin: 0 0 0 2px;
  border: 1px solid transparent;
  border-radius: 3px;
  cursor: pointer;
}
body.dark .editor-toolbar a.active, body.dark .editor-toolbar a:hover {
  background: #191e3a;
  border-color: #191e3a;
}
body.dark .editor-toolbar a:before {
  line-height: 30px;
}
body.dark .editor-toolbar i.separator {
  display: inline-block;
  width: 0;
  border-left: 1px solid #1b2e4b;
  border-right: 1px solid #1b2e4b;
  color: transparent;
  text-indent: -10px;
  margin: 0 6px;
}
body.dark .editor-toolbar a.fa-header-x:after {
  font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 65%;
  vertical-align: text-bottom;
  position: relative;
  top: 2px;
}
body.dark .editor-toolbar a.fa-header-1:after {
  content: "1";
}
body.dark .editor-toolbar a.fa-header-2:after {
  content: "2";
}
body.dark .editor-toolbar a.fa-header-3:after {
  content: "3";
}
body.dark .editor-toolbar a.fa-header-bigger:after {
  content: "▲";
}
body.dark .editor-toolbar a.fa-header-smaller:after {
  content: "▼";
}
body.dark .editor-toolbar.disabled-for-preview a:not(.no-disable) {
  pointer-events: none;
  background: #060818;
  border-color: #060818;
  text-shadow: inherit;
}
@media only screen and (max-width: 700px) {
  body.dark .editor-toolbar a.no-mobile {
    display: none;
  }
}
body.dark .editor-statusbar {
  padding: 8px 10px;
  font-size: 12px;
  color: #888ea8;
  text-align: right;
}
body.dark .editor-statusbar span {
  display: inline-block;
  min-width: 4em;
  margin-left: 1em;
}
body.dark .editor-preview, body.dark .editor-preview-side {
  padding: 10px;
  background: #0e1726;
  overflow: auto;
  display: none;
  box-sizing: border-box;
}
body.dark .editor-statusbar .lines:before {
  content: "lines: ";
}
body.dark .editor-statusbar .words:before {
  content: "words: ";
}
body.dark .editor-statusbar .characters:before {
  content: "characters: ";
}
body.dark .editor-preview {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 7;
}
body.dark .editor-preview-side {
  position: fixed;
  bottom: 0;
  width: 50%;
  top: 50px;
  right: 0;
  z-index: 9;
  border: 1px solid #ddd;
}
body.dark .editor-preview-active, body.dark .editor-preview-active-side {
  display: block;
}
body.dark .editor-preview-side > p {
  margin-top: 0;
}
body.dark .editor-preview > p {
  margin-top: 0;
}
body.dark .editor-preview pre {
  background: #eee;
  margin-bottom: 10px;
}
body.dark .editor-preview-side pre {
  background: #eee;
  margin-bottom: 10px;
}
body.dark .editor-preview table td, body.dark .editor-preview table th {
  border: 1px solid #ddd;
  padding: 5px;
}
body.dark .editor-preview-side table td, body.dark .editor-preview-side table th {
  border: 1px solid #ddd;
  padding: 5px;
}
body.dark .CodeMirror .CodeMirror-code .cm-tag {
  color: #63a35c;
}
body.dark .CodeMirror .CodeMirror-code .cm-attribute {
  color: #795da3;
}
body.dark .CodeMirror .CodeMirror-code .cm-string {
  color: #183691;
}
body.dark .CodeMirror .CodeMirror-selected {
  background: #d9d9d9;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-1 {
  font-size: 200%;
  line-height: 200%;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-2 {
  font-size: 160%;
  line-height: 160%;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-3 {
  font-size: 125%;
  line-height: 125%;
}
body.dark .CodeMirror .CodeMirror-code .cm-header-4 {
  font-size: 110%;
  line-height: 110%;
}
body.dark .CodeMirror .CodeMirror-code .cm-comment {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}
body.dark .CodeMirror .CodeMirror-code .cm-link {
  color: #7f8c8d;
}
body.dark .CodeMirror .CodeMirror-code .cm-url {
  color: #aab2b3;
}
body.dark .CodeMirror .CodeMirror-code .cm-strikethrough {
  text-decoration: line-through;
}
body.dark .CodeMirror .CodeMirror-placeholder {
  opacity: 0.5;
}
body.dark .CodeMirror .cm-spell-error:not(.cm-url):not(.cm-comment):not(.cm-tag):not(.cm-word) {
  background: rgba(255, 0, 0, 0.15);
}/*# sourceMappingURL=simplemde.min.css.map */