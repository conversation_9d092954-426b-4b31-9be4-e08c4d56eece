@charset "UTF-8";
/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*!
 * Quill Editor v1.3.6
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */
body.dark .ql-container {
  box-sizing: border-box;
  height: 100%;
  margin: 0px;
  position: relative;
}
body.dark .ql-container.ql-disabled .ql-tooltip {
  visibility: hidden;
}
body.dark .ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {
  pointer-events: none;
}
body.dark .ql-clipboard {
  left: -100000px;
  height: 1px;
  overflow-y: hidden;
  position: absolute;
  top: 50%;
}
body.dark .ql-clipboard p {
  margin: 0;
  padding: 0;
}
body.dark .ql-editor {
  box-sizing: border-box;
  line-height: 1.42;
  height: 100%;
  outline: none;
  overflow-y: auto;
  padding: 12px 15px;
  -o-tab-size: 4;
     tab-size: 4;
  -moz-tab-size: 4;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}
body.dark .ql-editor > * {
  cursor: text;
}
body.dark .ql-editor p, body.dark .ql-editor ol, body.dark .ql-editor ul, body.dark .ql-editor pre, body.dark .ql-editor blockquote, body.dark .ql-editor h1, body.dark .ql-editor h2, body.dark .ql-editor h3, body.dark .ql-editor h4, body.dark .ql-editor h5, body.dark .ql-editor h6 {
  margin: 0;
  padding: 0;
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
body.dark .ql-editor ol, body.dark .ql-editor ul {
  padding-left: 1.5em;
}
body.dark .ql-editor ol > li {
  list-style-type: none;
}
body.dark .ql-editor ul > li {
  list-style-type: none;
}
body.dark .ql-editor ul > li::before {
  content: "•";
}
body.dark .ql-editor ul[data-checked=true], body.dark .ql-editor ul[data-checked=false] {
  pointer-events: none;
}
body.dark .ql-editor ul[data-checked=true] > li *, body.dark .ql-editor ul[data-checked=false] > li * {
  pointer-events: all;
}
body.dark .ql-editor ul[data-checked=true] > li::before, body.dark .ql-editor ul[data-checked=false] > li::before {
  color: #777;
  cursor: pointer;
  pointer-events: all;
}
body.dark .ql-editor ul[data-checked=true] > li::before {
  content: "☑";
}
body.dark .ql-editor ul[data-checked=false] > li::before {
  content: "☐";
}
body.dark .ql-editor li::before {
  display: inline-block;
  white-space: nowrap;
  width: 1.2em;
}
body.dark .ql-editor li:not(.ql-direction-rtl)::before {
  margin-left: -1.5em;
  margin-right: 0.3em;
  text-align: right;
}
body.dark .ql-editor li.ql-direction-rtl::before {
  margin-left: 0.3em;
  margin-right: -1.5em;
}
body.dark .ql-editor ol li:not(.ql-direction-rtl), body.dark .ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 1.5em;
}
body.dark .ql-editor ol li.ql-direction-rtl, body.dark .ql-editor ul li.ql-direction-rtl {
  padding-right: 1.5em;
}
body.dark .ql-editor ol li {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  counter-increment: list-0;
}
body.dark .ql-editor ol li:before {
  content: counter(list-0, decimal) ". ";
}
body.dark .ql-editor ol li.ql-indent-1 {
  counter-increment: list-1;
  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
body.dark .ql-editor ol li.ql-indent-1:before {
  content: counter(list-1, lower-alpha) ". ";
}
body.dark .ql-editor ol li.ql-indent-2 {
  counter-increment: list-2;
  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
body.dark .ql-editor ol li.ql-indent-2:before {
  content: counter(list-2, lower-roman) ". ";
}
body.dark .ql-editor ol li.ql-indent-3 {
  counter-increment: list-3;
  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
}
body.dark .ql-editor ol li.ql-indent-3:before {
  content: counter(list-3, decimal) ". ";
}
body.dark .ql-editor ol li.ql-indent-4 {
  counter-increment: list-4;
  counter-reset: list-5 list-6 list-7 list-8 list-9;
}
body.dark .ql-editor ol li.ql-indent-4:before {
  content: counter(list-4, lower-alpha) ". ";
}
body.dark .ql-editor ol li.ql-indent-5 {
  counter-increment: list-5;
  counter-reset: list-6 list-7 list-8 list-9;
}
body.dark .ql-editor ol li.ql-indent-5:before {
  content: counter(list-5, lower-roman) ". ";
}
body.dark .ql-editor ol li.ql-indent-6 {
  counter-increment: list-6;
  counter-reset: list-7 list-8 list-9;
}
body.dark .ql-editor ol li.ql-indent-6:before {
  content: counter(list-6, decimal) ". ";
}
body.dark .ql-editor ol li.ql-indent-7 {
  counter-increment: list-7;
  counter-reset: list-8 list-9;
}
body.dark .ql-editor ol li.ql-indent-7:before {
  content: counter(list-7, lower-alpha) ". ";
}
body.dark .ql-editor ol li.ql-indent-8 {
  counter-increment: list-8;
  counter-reset: list-9;
}
body.dark .ql-editor ol li.ql-indent-8:before {
  content: counter(list-8, lower-roman) ". ";
}
body.dark .ql-editor ol li.ql-indent-9 {
  counter-increment: list-9;
}
body.dark .ql-editor ol li.ql-indent-9:before {
  content: counter(list-9, decimal) ". ";
}
body.dark .ql-editor .ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 3em;
}
body.dark .ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 4.5em;
}
body.dark .ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 3em;
}
body.dark .ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 4.5em;
}
body.dark .ql-editor .ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 6em;
}
body.dark .ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 7.5em;
}
body.dark .ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 6em;
}
body.dark .ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 7.5em;
}
body.dark .ql-editor .ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 9em;
}
body.dark .ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 10.5em;
}
body.dark .ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 9em;
}
body.dark .ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 10.5em;
}
body.dark .ql-editor .ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 12em;
}
body.dark .ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 13.5em;
}
body.dark .ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 12em;
}
body.dark .ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 13.5em;
}
body.dark .ql-editor .ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 15em;
}
body.dark .ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 16.5em;
}
body.dark .ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 15em;
}
body.dark .ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 16.5em;
}
body.dark .ql-editor .ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 18em;
}
body.dark .ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 19.5em;
}
body.dark .ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 18em;
}
body.dark .ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 19.5em;
}
body.dark .ql-editor .ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 21em;
}
body.dark .ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 22.5em;
}
body.dark .ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 21em;
}
body.dark .ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 22.5em;
}
body.dark .ql-editor .ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 24em;
}
body.dark .ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 25.5em;
}
body.dark .ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 24em;
}
body.dark .ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 25.5em;
}
body.dark .ql-editor .ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 27em;
}
body.dark .ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 28.5em;
}
body.dark .ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 27em;
}
body.dark .ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 28.5em;
}
body.dark .ql-editor .ql-video {
  display: block;
  max-width: 100%;
}
body.dark .ql-editor .ql-video.ql-align-center {
  margin: 0 auto;
}
body.dark .ql-editor .ql-video.ql-align-right {
  margin: 0 0 0 auto;
}
body.dark .ql-editor .ql-bg-black {
  background-color: #000;
}
body.dark .ql-editor .ql-bg-red {
  background-color: #e60000;
}
body.dark .ql-editor .ql-bg-orange {
  background-color: #f90;
}
body.dark .ql-editor .ql-bg-yellow {
  background-color: #ff0;
}
body.dark .ql-editor .ql-bg-green {
  background-color: #008a00;
}
body.dark .ql-editor .ql-bg-blue {
  background-color: #06c;
}
body.dark .ql-editor .ql-bg-purple {
  background-color: #93f;
}
body.dark .ql-editor .ql-color-white {
  color: #fff;
}
body.dark .ql-editor .ql-color-red {
  color: #e60000;
}
body.dark .ql-editor .ql-color-orange {
  color: #f90;
}
body.dark .ql-editor .ql-color-yellow {
  color: #ff0;
}
body.dark .ql-editor .ql-color-green {
  color: #008a00;
}
body.dark .ql-editor .ql-color-blue {
  color: #06c;
}
body.dark .ql-editor .ql-color-purple {
  color: #93f;
}
body.dark .ql-editor .ql-font-serif {
  font-family: Georgia, Times New Roman, serif;
}
body.dark .ql-editor .ql-font-monospace {
  font-family: Monaco, Courier New, monospace;
}
body.dark .ql-editor .ql-size-small {
  font-size: 0.75em;
}
body.dark .ql-editor .ql-size-large {
  font-size: 1.5em;
}
body.dark .ql-editor .ql-size-huge {
  font-size: 2.5em;
}
body.dark .ql-editor .ql-direction-rtl {
  direction: rtl;
  text-align: inherit;
}
body.dark .ql-editor .ql-align-center {
  text-align: center;
}
body.dark .ql-editor .ql-align-justify {
  text-align: justify;
}
body.dark .ql-editor .ql-align-right {
  text-align: right;
}
body.dark .ql-editor.ql-blank::before {
  color: #bfc9d4;
  content: attr(data-placeholder);
  font-style: italic;
  left: 15px;
  pointer-events: none;
  position: absolute;
  right: 15px;
}
body.dark .ql-snow {
  box-sizing: border-box;
}
body.dark .ql-snow.ql-toolbar:after, body.dark .ql-snow .ql-toolbar:after {
  clear: both;
  content: "";
  display: table;
}
body.dark .ql-snow.ql-toolbar button, body.dark .ql-snow .ql-toolbar button {
  background: none;
  border: none;
  cursor: pointer;
  display: inline-block;
  float: left;
  height: 24px;
  padding: 3px 5px;
  width: 28px;
}
body.dark .ql-snow.ql-toolbar button svg, body.dark .ql-snow .ql-toolbar button svg {
  float: left;
  height: 100%;
}
body.dark .ql-snow.ql-toolbar button:active:hover, body.dark .ql-snow .ql-toolbar button:active:hover {
  outline: none;
}
body.dark .ql-snow.ql-toolbar input.ql-image[type=file], body.dark .ql-snow .ql-toolbar input.ql-image[type=file] {
  display: none;
}
body.dark .ql-snow.ql-toolbar button:hover, body.dark .ql-snow .ql-toolbar button:hover, body.dark .ql-snow.ql-toolbar button:focus, body.dark .ql-snow .ql-toolbar button:focus, body.dark .ql-snow.ql-toolbar button.ql-active, body.dark .ql-snow .ql-toolbar button.ql-active, body.dark .ql-snow.ql-toolbar .ql-picker-label:hover, body.dark .ql-snow .ql-toolbar .ql-picker-label:hover, body.dark .ql-snow.ql-toolbar .ql-picker-label.ql-active, body.dark .ql-snow .ql-toolbar .ql-picker-label.ql-active, body.dark .ql-snow.ql-toolbar .ql-picker-item:hover, body.dark .ql-snow .ql-toolbar .ql-picker-item:hover, body.dark .ql-snow.ql-toolbar .ql-picker-item.ql-selected, body.dark .ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: #bfc9d4;
}
body.dark .ql-snow.ql-toolbar button:hover .ql-fill, body.dark .ql-snow .ql-toolbar button:hover .ql-fill, body.dark .ql-snow.ql-toolbar button:focus .ql-fill, body.dark .ql-snow .ql-toolbar button:focus .ql-fill, body.dark .ql-snow.ql-toolbar button.ql-active .ql-fill, body.dark .ql-snow .ql-toolbar button.ql-active .ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill, body.dark .ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill, body.dark .ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill, body.dark .ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill, body.dark .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: #bfc9d4;
}
body.dark .ql-snow.ql-toolbar button:hover .ql-stroke, body.dark .ql-snow .ql-toolbar button:hover .ql-stroke, body.dark .ql-snow.ql-toolbar button:focus .ql-stroke, body.dark .ql-snow .ql-toolbar button:focus .ql-stroke, body.dark .ql-snow.ql-toolbar button.ql-active .ql-stroke, body.dark .ql-snow .ql-toolbar button.ql-active .ql-stroke, body.dark .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke, body.dark .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke, body.dark .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke, body.dark .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke, body.dark .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke, body.dark .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke, body.dark .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke, body.dark .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke, body.dark .ql-snow.ql-toolbar button:hover .ql-stroke-miter, body.dark .ql-snow .ql-toolbar button:hover .ql-stroke-miter, body.dark .ql-snow.ql-toolbar button:focus .ql-stroke-miter, body.dark .ql-snow .ql-toolbar button:focus .ql-stroke-miter, body.dark .ql-snow.ql-toolbar button.ql-active .ql-stroke-miter, body.dark .ql-snow .ql-toolbar button.ql-active .ql-stroke-miter, body.dark .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter, body.dark .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter, body.dark .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter, body.dark .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter, body.dark .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter, body.dark .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter, body.dark .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter, body.dark .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: #bfc9d4;
}
body.dark .ql-snow * {
  box-sizing: border-box;
}
body.dark .ql-snow .ql-hidden {
  display: none;
}
body.dark .ql-snow .ql-out-bottom, body.dark .ql-snow .ql-out-top {
  visibility: hidden;
}
body.dark .ql-snow .ql-tooltip {
  position: absolute;
  transform: translateY(10px);
}
body.dark .ql-snow .ql-tooltip a {
  cursor: pointer;
  text-decoration: none;
}
body.dark .ql-snow .ql-tooltip.ql-flip {
  transform: translateY(-10px);
}
body.dark .ql-snow .ql-formats {
  display: inline-block;
  vertical-align: middle;
}
body.dark .ql-snow .ql-formats:after {
  clear: both;
  content: "";
  display: table;
}
body.dark .ql-snow .ql-stroke {
  fill: none;
  stroke: #009688;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}
body.dark .ql-snow .ql-stroke-miter {
  fill: none;
  stroke: #009688;
  stroke-miterlimit: 10;
  stroke-width: 2;
}
body.dark .ql-snow .ql-fill, body.dark .ql-snow .ql-stroke.ql-fill {
  fill: #009688;
}
body.dark .ql-snow .ql-empty {
  fill: none;
}
body.dark .ql-snow .ql-even {
  fill-rule: evenodd;
}
body.dark .ql-snow .ql-thin, body.dark .ql-snow .ql-stroke.ql-thin {
  stroke-width: 1;
}
body.dark .ql-snow .ql-transparent {
  opacity: 0.4;
}
body.dark .ql-snow .ql-direction svg:last-child {
  display: none;
}
body.dark .ql-snow .ql-direction.ql-active svg:last-child {
  display: inline;
}
body.dark .ql-snow .ql-direction.ql-active svg:first-child {
  display: none;
}
body.dark .ql-snow .ql-editor a {
  text-decoration: underline;
}
body.dark .ql-snow .ql-editor blockquote {
  border-left: 4px solid #ccc;
  margin-bottom: 5px;
  margin-top: 5px;
  padding-left: 16px;
}
body.dark .ql-snow .ql-editor code {
  background-color: #f0f0f0;
  border-radius: 3px;
}
body.dark .ql-snow .ql-editor pre {
  background-color: #f0f0f0;
  border-radius: 3px;
  white-space: pre-wrap;
  margin-bottom: 5px;
  margin-top: 5px;
  padding: 5px 10px;
}
body.dark .ql-snow .ql-editor code {
  font-size: 85%;
  padding: 2px 4px;
}
body.dark .ql-snow .ql-editor pre.ql-syntax {
  background-color: #23241f;
  color: #f8f8f2;
  overflow: visible;
}
body.dark .ql-snow .ql-editor img {
  max-width: 100%;
}
body.dark .ql-snow .ql-picker {
  color: #e0e6ed;
  display: inline-block;
  float: left;
  font-size: 14px;
  font-weight: 500;
  height: 24px;
  position: relative;
  vertical-align: middle;
}
body.dark .ql-snow .ql-picker-label {
  cursor: pointer;
  display: inline-block;
  height: 100%;
  padding-left: 8px;
  padding-right: 2px;
  position: relative;
  width: 100%;
}
body.dark .ql-snow .ql-picker-label::before {
  display: inline-block;
  line-height: 22px;
}
body.dark .ql-snow .ql-picker-options {
  background-color: #191e3a;
  display: none;
  min-width: 100%;
  padding: 4px 8px;
  position: absolute;
  white-space: nowrap;
}
body.dark .ql-snow .ql-picker-options .ql-picker-item {
  cursor: pointer;
  display: block;
  padding-bottom: 5px;
  padding-top: 5px;
}
body.dark .ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: #ccc;
  z-index: 2;
}
body.dark .ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: #ccc;
}
body.dark .ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: #ccc;
}
body.dark .ql-snow .ql-picker.ql-expanded .ql-picker-options {
  display: block;
  margin-top: -1px;
  top: 100%;
  z-index: 1;
}
body.dark .ql-snow .ql-color-picker, body.dark .ql-snow .ql-icon-picker {
  width: 28px;
}
body.dark .ql-snow .ql-color-picker .ql-picker-label, body.dark .ql-snow .ql-icon-picker .ql-picker-label {
  padding: 2px 4px;
}
body.dark .ql-snow .ql-color-picker .ql-picker-label svg {
  right: 4px;
}
body.dark .ql-snow .ql-icon-picker .ql-picker-label svg {
  right: 4px;
}
body.dark .ql-snow .ql-icon-picker .ql-picker-options {
  padding: 4px 0px;
}
body.dark .ql-snow .ql-icon-picker .ql-picker-item {
  height: 24px;
  width: 24px;
  padding: 2px 4px;
}
body.dark .ql-snow .ql-color-picker .ql-picker-options {
  padding: 3px 5px;
  width: 152px;
}
body.dark .ql-snow .ql-color-picker .ql-picker-item {
  border: 1px solid transparent;
  float: left;
  height: 16px;
  margin: 2px;
  padding: 0px;
  width: 16px;
}
body.dark .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  position: absolute;
  margin-top: -9px;
  right: 0;
  top: 50%;
  width: 18px;
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""])::before, body.dark .ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""])::before, body.dark .ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""])::before, body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""])::before, body.dark .ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""])::before, body.dark .ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""])::before {
  content: attr(data-label);
}
body.dark .ql-snow .ql-picker.ql-header {
  width: 98px;
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label::before, body.dark .ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "Normal";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before, body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "Heading 1";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before, body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "Heading 2";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before, body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "Heading 3";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before, body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "Heading 4";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before, body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "Heading 5";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before {
  content: "Heading 6";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "Heading 6";
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  font-size: 2em;
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  font-size: 1.5em;
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  font-size: 1.17em;
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  font-size: 1em;
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  font-size: 0.83em;
}
body.dark .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  font-size: 0.67em;
}
body.dark .ql-snow .ql-picker.ql-font {
  width: 108px;
}
body.dark .ql-snow .ql-picker.ql-font .ql-picker-label::before, body.dark .ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "Sans Serif";
}
body.dark .ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]::before, body.dark .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  content: "Serif";
}
body.dark .ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before {
  content: "Monospace";
}
body.dark .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  content: "Monospace";
}
body.dark .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  font-family: Georgia, Times New Roman, serif;
}
body.dark .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  font-family: Monaco, Courier New, monospace;
}
body.dark .ql-snow .ql-picker.ql-size {
  width: 98px;
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-label::before, body.dark .ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "Normal";
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]::before, body.dark .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  content: "Small";
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]::before, body.dark .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  content: "Large";
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]::before {
  content: "Huge";
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  content: "Huge";
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  font-size: 10px;
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  font-size: 18px;
}
body.dark .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  font-size: 32px;
}
body.dark .ql-snow .ql-color-picker.ql-background .ql-picker-item {
  background-color: #fff;
}
body.dark .ql-snow .ql-color-picker.ql-color .ql-picker-item {
  background-color: #000;
}
@media (pointer: coarse) {
  body.dark .ql-snow.ql-toolbar button:hover:not(.ql-active), body.dark .ql-snow .ql-toolbar button:hover:not(.ql-active) {
    color: #444;
  }
  body.dark .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill, body.dark .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill, body.dark .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill, body.dark .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {
    fill: #444;
  }
  body.dark .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke, body.dark .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke, body.dark .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter, body.dark .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {
    stroke: #444;
  }
}
body.dark .ql-toolbar.ql-snow {
  border: 1px solid #1b2e4b;
  box-sizing: border-box;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  padding: 8px;
  border-radius: 6px;
}
body.dark .ql-toolbar.ql-snow .ql-formats {
  margin-right: 15px;
}
body.dark .ql-toolbar.ql-snow .ql-picker-label {
  border: 1px solid transparent;
  color: #009688;
}
body.dark .ql-toolbar.ql-snow .ql-picker-options {
  border: 1px solid transparent;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 8px;
}
body.dark .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: #0e1726;
}
body.dark .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  border-color: #0e1726;
  border-color: #191e3a;
  border-radius: 6px;
}
body.dark .ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected, body.dark .ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover {
  border-color: #000;
}
body.dark .ql-toolbar.ql-snow + .ql-container.ql-snow {
  border-top: 0px;
  margin-top: 28px;
  padding: 13px 0;
  border: 1px solid #1b2e4b;
  border-radius: 6px;
}
body.dark .ql-snow .ql-tooltip {
  background-color: #fff;
  border: 1px solid #3b3f5c;
  box-shadow: 0px 0px 5px #ddd;
  color: #444;
  padding: 5px 12px;
  white-space: nowrap;
}
body.dark .ql-snow .ql-tooltip::before {
  content: "Visit URL:";
  line-height: 26px;
  margin-right: 8px;
}
body.dark .ql-snow .ql-tooltip input[type=text] {
  display: none;
  border: 1px solid #ccc;
  font-size: 13px;
  height: 26px;
  margin: 0px;
  padding: 3px 5px;
  width: 170px;
}
body.dark .ql-snow .ql-tooltip a {
  line-height: 26px;
}
body.dark .ql-snow .ql-tooltip a.ql-preview {
  display: inline-block;
  max-width: 200px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
}
body.dark .ql-snow .ql-tooltip a.ql-action::after {
  border-right: 1px solid #ccc;
  content: "Edit";
  margin-left: 16px;
  padding-right: 8px;
}
body.dark .ql-snow .ql-tooltip a.ql-remove::before {
  content: "Remove";
  margin-left: 8px;
}
body.dark .ql-snow .ql-tooltip.ql-editing a.ql-preview, body.dark .ql-snow .ql-tooltip.ql-editing a.ql-remove {
  display: none;
}
body.dark .ql-snow .ql-tooltip.ql-editing input[type=text] {
  display: inline-block;
}
body.dark .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "Save";
  padding-right: 0px;
}
body.dark .ql-snow .ql-tooltip[data-mode=link]::before {
  content: "Enter link:";
}
body.dark .ql-snow .ql-tooltip[data-mode=formula]::before {
  content: "Enter formula:";
}
body.dark .ql-snow .ql-tooltip[data-mode=video]::before {
  content: "Enter video:";
}
body.dark .ql-snow a {
  color: #bfc9d4;
}
body.dark .ql-container.ql-snow {
  border: none;
}/*# sourceMappingURL=quill.snow.css.map */