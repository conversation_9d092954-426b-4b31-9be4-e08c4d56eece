/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .bs-stepper-content {
  width: 100%;
}
body.dark .bs-stepper .step.crossed + .line {
  background-color: #4361ee;
}
body.dark .step.crossed .step-trigger.disabled .bs-stepper-circle, body.dark .step.crossed .step-trigger:disabled .bs-stepper-circle {
  background-color: #4361ee;
  color: #fff;
}
body.dark .bs-stepper .line, body.dark .bs-stepper-line {
  background-color: rgba(255, 255, 255, 0.12);
}
body.dark .bs-stepper-circle {
  background-color: #bfc9d4;
}
body.dark .bs-stepper-circle svg {
  width: 16px;
  height: 16px;
}
body.dark .bs-stepper .step-trigger {
  color: #bfc9d4;
  font-weight: 200;
  letter-spacing: 1px;
}
body.dark .bs-stepper .step-trigger.disabled, body.dark .bs-stepper .step-trigger:disabled {
  opacity: 0.45;
}
body.dark .bs-stepper .step-trigger.disabled .bs-stepper-circle, body.dark .bs-stepper .step-trigger:disabled .bs-stepper-circle {
  color: #000;
  font-weight: 700;
}
body.dark .active .bs-stepper-circle {
  background-color: #4361ee;
}
body.dark .bs-stepper-label:focus {
  color: #4361ee;
}

/* 
    ================
        Vertical
    ================
*/
body.dark .bs-stepper.vertical .bs-stepper-header {
  display: block;
}
body.dark .bs-stepper.vertical .step-trigger {
  padding: 0;
  padding-bottom: 15px;
}
body.dark .bs-stepper.vertical .bs-stepper-content .content:not(.active) {
  display: none;
}
body.dark .bs-stepper.vertical .line {
  width: 1px;
  height: 25px;
  margin-bottom: 15px;
}
body.dark .vertical .bs-stepper-line {
  width: 1px;
  height: 25px;
  margin-bottom: 15px;
}
@media (max-width: 575px) {
  body.dark .bs-stepper-header {
    display: block;
  }
  body.dark .bs-stepper.vertical {
    display: block;
  }
  body.dark .bs-stepper .line {
    display: none;
  }
  body.dark .bs-stepper-line {
    display: none;
  }
  body.dark .bs-stepper .step-trigger {
    padding: 8px 0;
  }
  body.dark .bs-stepper-content {
    padding: 0;
    padding: 20px 0 0;
  }
}/*# sourceMappingURL=custom-bsStepper.css.map */