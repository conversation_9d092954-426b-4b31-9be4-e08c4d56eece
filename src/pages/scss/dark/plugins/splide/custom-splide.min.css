/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .splide__slide {
  margin-right: 10px;
}
body.dark .splide__track img {
  width: 100%;
  border-radius: 8px;
}
body.dark .splide__pagination {
  bottom: -50px;
}
body.dark .splide__pagination__page {
  background-color: #fff;
  height: 12px;
  width: 12px;
  border-radius: 8px;
  opacity: 1;
}
body.dark .splide__pagination__page.is-active {
  transform: none;
  background-color: #00ab55;
  color: #fff;
}
body.dark .splide__pagination.numberic-pagination {
  bottom: -50px;
}
body.dark .splide__pagination.numberic-pagination .splide__pagination__page {
  background-color: #fff;
  height: 30px;
  width: 30px;
  border-radius: 8px;
  opacity: 1;
}
body.dark .splide__pagination.numberic-pagination .splide__pagination__page.is-active {
  transform: none;
  background-color: #00ab55;
  color: #fff;
}
body.dark .splide__arrow {
  background-color: #fff;
  opacity: 1;
}
body.dark .splide__arrow svg {
  fill: #000;
}
body.dark .splide--ttb > .splide__arrows .splide__arrow--next, body.dark .splide--ttb > .splide__slider > .splide__track > .splide__arrows .splide__arrow--next, body.dark .splide--ttb > .splide__track > .splide__arrows .splide__arrow--next {
  bottom: -3em;
}
body.dark .splide--ttb > .splide__arrows .splide__arrow--prev, body.dark .splide--ttb > .splide__slider > .splide__track > .splide__arrows .splide__arrow--prev, body.dark .splide--ttb > .splide__track > .splide__arrows .splide__arrow--prev {
  top: -3em;
}
@media (max-width: 640px) {
  body.dark .splide-mainThubnail .splide__list li {
    height: auto !important;
    margin-bottom: 10px;
  }
}/*# sourceMappingURL=custom-splide.min.css.map */