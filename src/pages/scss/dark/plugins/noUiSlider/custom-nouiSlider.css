/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .noUi-target {
  background: #191e3a;
  border-radius: 4px;
  border: 1px solid #191e3a;
  box-shadow: none;
}
body.dark .noUi-horizontal {
  height: 8px;
}
body.dark .noUi-horizontal .noUi-handle {
  width: 25px;
  height: 20px;
  top: -8px;
  border: 1px solid #506690;
  background: #506690;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .noUi-handle:after, body.dark .noUi-handle:before {
  display: none;
}
body.dark .noUi-connect {
  background: #009688;
}
body.dark .noUi-tooltip {
  border: 1px solid #191e3a;
  border-radius: 8px;
  background: #191e3a;
  color: #009688;
  padding: 6px 14px;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .noUi-horizontal .noUi-tooltip {
  bottom: 148%;
}
body.dark .example-val {
  font-weight: 700;
  font-size: 14px;
  color: #009688;
}
body.dark .example-val span.precentage-val {
  display: inline-block;
  background: #191e3a;
  border-radius: 5px;
  color: #009688;
  border: 1px solid #191e3a;
  padding: 4px 6px;
  font-size: 14px;
}/*# sourceMappingURL=custom-nouiSlider.css.map */