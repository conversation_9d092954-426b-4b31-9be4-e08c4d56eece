/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .filepond {
  margin: 0 auto;
}
body.dark .profile-image .filepond {
  width: 120px;
  height: 120px !important;
}
body.dark .multiple-file-upload .filepond {
  width: 100%;
}
body.dark .filepond--drop-label {
  cursor: pointer;
  font-size: 12px;
}
body.dark .filepond--drop-label label {
  cursor: pointer;
  font-size: 12px;
}
body.dark .filepond .no-image-placeholder {
  display: inline-block;
  margin-bottom: 5px;
}
body.dark .filepond--panel {
  background-color: #1b2e4b !important;
}
body.dark .filepond--panel[data-scalable=true] {
  background-color: #1b2e4b !important;
}
body.dark .filepond--root .filepond--drop-label, body.dark .filepond--drip, body.dark .filepond--panel-center, body.dark .filepond--panel-top, body.dark .filepond--panel-bottom {
  background-color: #1b2e4b;
  border-radius: 9px;
}
body.dark .filepond--file, body.dark .filepond--file-action-button {
  background-color: #060818 !important;
}
body.dark .filepond--file-info {
  background-color: #060818 !important;
}
body.dark .filepond--file-info .filepond--file-info-main {
  background-color: #060818 !important;
}
body.dark .filepond--file .filepond--file-status {
  background-color: #060818 !important;
}
body.dark [data-filepond-item-state=processing-complete] .filepond--item-panel {
  background-color: #369763 !important;
  background-color: #369763 !important;
  background-color: #369763 !important;
}
body.dark .filepond--file-action-button.filepond--file-action-button svg {
  background: #1b2e4b;
  border-radius: 60px;
  color: #bfc9d4;
}
body.dark .filepond--file-action-button:focus, body.dark .filepond--file-action-button:hover {
  box-shadow: none;
}
body.dark .filepond .no-image-placeholder svg {
  height: 34px;
  width: 34px;
  stroke-width: 1.2;
  color: #000;
  fill: rgba(0, 0, 0, 0.1215686275);
}
body.dark .filepond .drag-para {
  margin-bottom: 0;
  font-size: 12px;
  color: #000;
  margin-top: 9px;
}
body.dark .filepond--root .filepond--credits {
  display: none;
}/*# sourceMappingURL=custom-filepond.css.map */