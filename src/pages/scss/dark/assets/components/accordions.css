/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark h1, body.dark h2, body.dark h3, body.dark h4, body.dark h5, body.dark h6 {
  color: #e0e6ed;
}

/*
    Basic
*/
body.dark .accordion .card {
  border: 2px solid #1b2e4b;
  border-radius: 6px;
  margin-bottom: 4px;
  background: #060818;
}
body.dark .accordion .card-header {
  background-color: #1b2e4b;
  color: #f8538d;
  border-radius: 0;
  padding: 0;
  position: relative;
  border-bottom: none;
}
body.dark .accordion .card-header section > div {
  padding: 13px 19px;
  cursor: pointer;
  display: block;
  font-size: 14px;
  letter-spacing: 1px;
}
body.dark .accordion .card-header section > div.collapsed {
  color: #888ea8;
}
body.dark .accordion .card-header section > div:not(.collapsed) {
  color: #22c7d5;
  border-bottom: 2px solid #1b2e4b;
  font-weight: 600;
}
body.dark .accordion .card-header section > div .icons {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 9px;
}
body.dark .accordion .card-header section > div .icons svg {
  width: 18px;
  transition: 0.5s;
  transform: rotate(0);
}
body.dark .accordion .card-header section > div[aria-expanded=true] .icons svg {
  transform: rotate(180deg);
}
body.dark .accordion .card .card-body p {
  color: #888ea8;
  letter-spacing: 1px;
  font-size: 13px;
}
body.dark .accordion .card .card-body p:not(:last-child) {
  margin-bottom: 10px;
}
body.dark .accordion .card .card-body ul {
  margin-bottom: 0;
}
body.dark .accordion .card .card-body ul li {
  font-size: 12px;
  letter-spacing: 1px;
}
body.dark .accordion .card .card-body ul li:not(:last-child) {
  margin-bottom: 5px;
}
body.dark .accordion .card .card-body ul li a {
  color: #888ea8;
  font-size: 13px;
  font-weight: 600;
}
body.dark .accordion .card .card-body ul li a:hover {
  color: #4361ee;
}
body.dark .accordion.no-outer-spacing {
  border: 1px solid #3b3f5c;
  border-radius: 6px;
}
body.dark .accordion.no-outer-spacing .card {
  margin-bottom: 0;
  border: none;
  border-radius: 0;
}
body.dark .accordion.no-outer-spacing .card:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
body.dark .accordion.no-outer-spacing .card:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}
body.dark .accordion.no-outer-spacing .card:not(:last-child) {
  border-bottom: 1px solid #3b3f5c;
}
body.dark .accordion.no-outer-spacing .card-header section > div:not(.collapsed) {
  border-bottom: none;
}

/*
    No Outer Spacing
*/
/*
    Accordin with Icons
*/
body.dark .accordion-icons .accordion-icon {
  display: inline-block;
  margin-right: 10px;
}
body.dark .accordion-icons .accordion-icon svg {
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  fill: rgba(0, 23, 55, 0.08);
}
body.dark .accordion-icons div:not(.collapsed) .accordion-icon svg {
  color: #22c7d5;
  fill: rgba(27, 85, 226, 0.2392156863);
}/*# sourceMappingURL=accordions.css.map */