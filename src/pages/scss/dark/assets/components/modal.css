/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .modal-backdrop {
  background: linear-gradient(75deg, rgba(22, 28, 36, 0.48) 0%, rgb(22, 28, 36) 100%);
}
body.dark .modal-backdrop.show {
  opacity: 0.8;
}
body.dark .modal-content {
  border: none;
  border-radius: 6px;
  background: #0e1726;
  border: 1px solid #191e3a;
}
body.dark .modal-content hr {
  border-top: 1px solid #191e3a;
}
body.dark .modal-content .modal-header {
  padding: 12px 26px;
  border: none;
  border-bottom: 1px solid #191e3a;
}
body.dark .modal-content .modal-header h5 {
  font-weight: 600;
  font-size: 20px;
  letter-spacing: 1px;
}
body.dark .modal-content .modal-header svg {
  width: 17px;
  color: #bfc9d4;
}
body.dark .modal-content .modal-header .btn-close {
  background: none;
  box-shadow: none;
  padding: 0;
  margin: 0;
  display: grid;
  opacity: 1;
}
body.dark .modal-content .modal-header .btn-close svg {
  width: 17px;
  height: 17px;
  color: #fff;
}
body.dark .modal-content .modal-body {
  padding: 26px 26px;
}
body.dark .modal-content .modal-body a:not(.btn) {
  color: #4361ee;
  font-weight: 600;
}
body.dark .modal-content .modal-body p {
  color: #888ea8;
  letter-spacing: 1px;
  font-size: 14px;
  line-height: 22px;
  text-align: left;
}
body.dark .modal-content .modal-body p:last-child {
  margin-bottom: 0;
}
body.dark .modal-content .modal-body p:not(:last-child) {
  margin-bottom: 10px;
}
body.dark .modal-content .modal-footer {
  border-top: 1px solid #191e3a;
}
body.dark .modal-content .modal-footer button.btn {
  font-weight: 600;
  padding: 10px 25px;
  letter-spacing: 1px;
}
body.dark .modal-content .modal-footer .btn.btn-primary {
  background-color: #4361ee;
  color: #fff;
  border: 1px solid #4361ee;
}

/*
    Modal Tabs
*/
body.dark .close {
  text-shadow: none;
  color: #bfc9d4;
}
body.dark .close:hover {
  color: #bfc9d4;
}
body.dark .nav-tabs {
  border-bottom: 1px solid #191e3a;
}
body.dark .nav-tabs svg {
  width: 20px;
  vertical-align: bottom;
}
body.dark .nav-tabs .nav-link.active {
  color: #e95f2b;
  background-color: #191e3a;
  border-color: #191e3a #191e3a #0e1726;
}
body.dark .nav-tabs .nav-link.active:after {
  color: #e95f2b;
}
body.dark .nav-tabs .nav-link:hover {
  border-color: #191e3a #191e3a #191e3a;
}

/*
    Modal Success
*/
body.dark .modal-success .modal-content {
  background-color: #0c272b;
}

/*
    Modal Video
*/
body.dark .modal-video .modal-content {
  background-color: transparent;
  border: none;
}
body.dark .modal-video .video-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}
body.dark .modal-video .modal#videoMedia1 .modal-header, body.dark .modal-video .modal#videoMedia2 .modal-header {
  border: none;
  padding: 12px 0;
  justify-content: end;
}
body.dark .modal-video .video-container iframe, body.dark .modal-video .video-container object, body.dark .modal-video .video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
body.dark .modal-video .modal#videoMedia1 .modal-header .close, body.dark .modal-video .modal#videoMedia2 .modal-header .close {
  color: #fff !important;
  opacity: 1;
}
body.dark .modal-video .modal-content .modal-header svg {
  color: #fff;
}

/*
    Modal Notification
*/
body.dark .modal-notification .modal-body .icon-content {
  margin: 0 0 20px 0px;
  display: inline-block;
  padding: 13px;
  border-radius: 50%;
  background: #bfc9d4;
}
body.dark .modal-notification .modal-body .icon-content svg {
  width: 36px;
  height: 36px;
  color: #1b2e4b;
  fill: rgba(0, 23, 55, 0.08);
}

/*
    Profile
*/
body.dark .profile-modal .modal-content {
  background-color: #805dca;
}
body.dark .profile-modal .modal-content .btn-close {
  font-size: 19px;
  font-weight: 600;
  line-height: 1;
  color: #fff;
  text-shadow: none;
  opacity: 1;
  text-align: right;
  background: none;
  margin-left: auto;
  box-shadow: none;
}
body.dark .profile-modal .modal-content .modal-header, body.dark .profile-modal .modal-content .modal-footer {
  border: none;
}
body.dark .profile-modal .modal-content .modal-body p {
  color: #fff;
}
body.dark .profile-modal .modal-content .modal-footer button.btn {
  box-shadow: none;
}
body.dark .modal#sliderModal .modal-content .modal-body button.btn-close {
  position: absolute;
  z-index: 2;
  right: 4px;
  top: -35px;
  opacity: 1;
  text-shadow: none;
  background: transparent;
  box-shadow: none;
}
body.dark .modal#sliderModal .modal-content .modal-body button.btn-close svg {
  color: #bfc9d4;
}
body.dark .modal#sliderModal .modal-content .modal-body button.btn-close:hover svg {
  color: #fff;
}

/*
    Form
*/
.inputForm-modal .modal-content .modal-body .form-group .input-group .input-group-text {
  background: transparent;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  border-right: none;
}
.inputForm-modal .modal-content .modal-body .form-group input {
  border-left: none;
  background: transparent;
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}
.inputForm-modal .modal-content .modal-body .form-group input:focus {
  border-color: #bfc9d4;
}/*# sourceMappingURL=modal.css.map */