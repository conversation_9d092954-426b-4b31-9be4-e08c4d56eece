/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .list-group-item {
  border: 1px solid #1b2e4b;
  padding: 10px 12px;
  background-color: transparent;
  color: #bfc9d4;
  margin-bottom: 0;
}
body.dark .list-group-item .form-check-input:not(:checked) {
  background-color: #515365;
  border-color: #515365;
}
body.dark .list-group-item.active {
  color: #fff;
  background-color: #805dca;
  border-color: transparent;
  box-shadow: 0 1px 15px 1px rgba(52, 40, 104, 0.15);
}
body.dark .list-group-item.active:hover, body.dark .list-group-item.active:focus {
  color: #e0e6ed;
  background-color: #805dca;
  box-shadow: 0px 0px 12px 1px rgba(113, 106, 202, 0.08);
}
body.dark .list-group-item.disabled, body.dark .list-group-item:disabled {
  background: rgba(80, 102, 144, 0.1607843137);
  color: #888ea8;
}
body.dark .new-control-indicator {
  background-color: #f1f2f3;
}
body.dark a.list-group-item.list-group-item-action.active i {
  color: #010156;
}
body.dark code {
  color: #e7515a;
}
body.dark .list-group-item-action:hover {
  color: #e0e6ed;
  background-color: #191e3a;
}
body.dark .list-group-item-action:focus {
  background-color: transparent;
  color: #bfc9d4;
}

/*------list group-----*/
/*
    Icons Meta
*/
body.dark .list-group.list-group-icons-meta .list-group-item.active .media svg {
  font-size: 27px;
  color: #fff;
}
body.dark .list-group.list-group-icons-meta .list-group-item.active .media .media-body h6, body.dark .list-group.list-group-icons-meta .list-group-item.active .media .media-body p {
  color: #fff;
  font-weight: 500;
}
body.dark .list-group.list-group-icons-meta .list-group-item .media svg {
  width: 20px;
  color: #22c7d5;
  height: 20px;
}
body.dark .list-group.list-group-icons-meta .list-group-item .media .media-body h6 {
  color: #888ea8;
  font-weight: 700;
  margin-bottom: 0;
  font-size: 15px;
  letter-spacing: 1px;
}
body.dark .list-group.list-group-icons-meta .list-group-item .media .media-body p {
  color: #888ea8;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
}
body.dark .list-group.list-group-media .list-group-item.active .media .media-body h6, body.dark .list-group.list-group-media .list-group-item.active .media .media-body p {
  color: #fff;
  font-weight: 500;
}
body.dark .list-group.list-group-media .list-group-item .media img {
  color: #4361ee;
  width: 42px;
  height: 42px;
}
body.dark .list-group.list-group-media .list-group-item .media .media-body {
  align-self: center;
}
body.dark .list-group.list-group-media .list-group-item .media .media-body h6 {
  color: #888ea8;
  font-weight: 700;
  margin-bottom: 0;
  font-size: 16px;
  letter-spacing: 1px;
}
body.dark .list-group.list-group-media .list-group-item .media .media-body p {
  color: #888ea8;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
}
body.dark .list-group.task-list-group .list-group-item-action.active {
  background-color: #191e3a;
  color: #fff;
}
body.dark .list-group.task-list-group .list-group-item-action.active .new-control.new-checkbox {
  color: #fff;
  font-size: 14px;
}

/*
    Image Meta
*/
/*
    task-list-group
*//*# sourceMappingURL=list-group.css.map */