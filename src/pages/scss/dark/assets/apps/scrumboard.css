/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/* Delete Modal*/
body.dark #deleteConformation .modal-content {
  border: 0;
  box-shadow: 2px 5px 17px 0 rgba(31, 45, 61, 0.1);
  padding: 30px;
}
body.dark #deleteConformation .modal-content .modal-header {
  border: none;
  padding: 0;
}
body.dark #deleteConformation .modal-content .modal-header .icon {
  padding: 7px 9px;
  background: rgba(231, 81, 90, 0.37);
  text-align: center;
  margin-right: 8px;
  border-radius: 50%;
}
body.dark #deleteConformation .modal-content .modal-header svg {
  width: 20px;
  color: #e7515a;
  fill: rgba(231, 81, 90, 0.37);
}
body.dark #deleteConformation .modal-content .modal-header .modal-title {
  color: #bfc9d4;
  font-size: 18px;
  font-weight: 700;
  align-self: center;
}
body.dark #deleteConformation .modal-content .modal-header .btn-close {
  color: #fff;
  background: none;
  opacity: 1;
  width: auto;
  height: auto;
  font-size: 20px;
}
body.dark #deleteConformation .modal-content .modal-body {
  padding: 28px 0;
}
body.dark #deleteConformation .modal-content .modal-body p {
  color: #888ea8;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 0;
}
body.dark #deleteConformation .modal-content .modal-footer {
  padding: 0;
  border: none;
}
body.dark #deleteConformation .modal-content .modal-footer [data-bs-dismiss=modal] {
  background-color: #fff;
  color: #e7515a;
  font-weight: 700;
  border: 1px solid #e8e8e8;
  padding: 10px 25px;
}
body.dark #deleteConformation .modal-content .modal-footer [data-remove=task] {
  color: #fff;
  font-weight: 600;
  padding: 10px 25px;
}
body.dark .task-list-section {
  display: flex;
  overflow-x: auto;
  flex-wrap: nowrap;
}
body.dark .task-list-container {
  min-width: 309px;
  padding: 0 15px;
  width: 320px;
}
body.dark .task-list-container:first-child {
  padding-left: 0;
}
body.dark .task-list-container:last-child {
  padding-right: 0;
}

/*  
    Connect Sorting Div
*/
body.dark .connect-sorting {
  padding: 15px;
  background: #0e1726;
  border-radius: 8px;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  border: 1px solid #0e1726;
}
body.dark .connect-sorting .task-container-header {
  display: flex;
  justify-content: space-between;
  padding: 18px 5px;
}
body.dark .connect-sorting .task-container-header .dropdown .dropdown-menu {
  padding: 11px;
}
body.dark .connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item {
  padding: 5px;
  font-size: 14px;
  font-weight: 700;
}
body.dark .connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item:hover {
  color: #009688;
}
body.dark .connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item.active, body.dark .connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}
body.dark .connect-sorting .task-container-header h6 {
  font-size: 16px;
  font-weight: 700;
  color: #bfc9d4;
}
body.dark .connect-sorting .add-s-task {
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  text-align: center;
}
body.dark .connect-sorting .add-s-task:hover {
  transform: translateY(-3px);
}
body.dark .connect-sorting .add-s-task .addTask {
  display: block;
  color: #bfc9d4;
  font-size: 13px;
  font-weight: 700;
  text-align: center;
  display: inline-block;
  cursor: pointer;
}
body.dark .connect-sorting .add-s-task .addTask:hover {
  color: #fff;
}
body.dark .connect-sorting .add-s-task .addTask svg {
  width: 16px;
  height: 16px;
  vertical-align: text-top;
}
body.dark .scrumboard .task-header {
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20px 20px 0 20px;
}
body.dark .scrumboard .task-header h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0;
  color: #bfc9d4;
}
body.dark .scrumboard .task-header svg.feather-edit-2 {
  width: 18px;
  height: 18px;
  color: #888ea8;
  vertical-align: middle;
  fill: rgba(0, 23, 55, 0.08);
  cursor: pointer;
  padding: 0;
  margin-right: 5px;
}
body.dark .scrumboard .task-header svg.feather-edit-2:hover {
  color: #009688;
  fill: rgba(0, 150, 136, 0.41);
}
body.dark .scrumboard .task-header svg.feather-trash-2 {
  color: #e7515a;
  margin-right: 6px;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  fill: rgba(231, 81, 90, 0.14);
  cursor: pointer;
}
body.dark .scrumboard .task-header svg.feather-trash-2:hover {
  fill: rgba(231, 81, 90, 0.37);
}
body.dark .scrumboard .card {
  background: #191e3a;
  border: none;
  border-radius: 4px;
  margin-bottom: 30px;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .scrumboard .card .card-body {
  padding: 0;
}
body.dark .scrumboard .card .card-body .task-body .task-bottom {
  display: flex;
  justify-content: space-between;
  padding: 12px 15px;
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 span {
  font-size: 13px;
  font-weight: 600;
  width: 17px;
  height: 17px;
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 span:hover {
  color: #009688;
  cursor: pointer;
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 span:hover svg {
  color: #009688;
  fill: rgba(27, 85, 226, 0.2392156863);
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 svg {
  width: 18px;
  vertical-align: bottom;
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 svg:not(:last-child) {
  margin-right: 5px;
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg {
  width: 18px;
  cursor: pointer;
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  fill: rgba(0, 23, 55, 0.08);
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-edit-2 {
  width: 18px;
  height: 18px;
  color: #888ea8;
  vertical-align: middle;
  fill: rgba(0, 23, 55, 0.08);
  cursor: pointer;
  padding: 0;
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-edit-2:hover {
  color: #009688;
  fill: rgba(0, 150, 136, 0.41);
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-trash-2 {
  color: #e7515a;
  margin-right: 6px;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  fill: rgba(231, 81, 90, 0.14);
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-trash-2:hover {
  fill: rgba(231, 81, 90, 0.37);
}
body.dark .scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg:not(:last-child) {
  margin-right: 5px;
}
body.dark .scrumboard .card.img-task .card-body .task-content {
  padding: 10px 10px 0 10px;
}
body.dark .scrumboard .card.img-task .card-body .task-content img {
  border-radius: 6px;
  height: 105px;
  width: 100%;
}
body.dark .scrumboard .card.simple-title-task .card-body .task-header {
  margin-bottom: 0;
  padding: 20px;
}
body.dark .scrumboard .card.simple-title-task .card-body .task-header div:nth-child(1) {
  width: 70%;
}
body.dark .scrumboard .card.simple-title-task .card-body .task-header div:nth-child(2) {
  width: 30%;
  text-align: right;
}
body.dark .scrumboard .card.simple-title-task .card-body .task-body .task-bottom {
  padding: 3px 15px 11px 15px;
}
body.dark .scrumboard .card.task-text-progress .card-body .task-content {
  margin-top: 20px;
}
body.dark .scrumboard .card.task-text-progress .card-body .task-content p {
  padding: 5px 20px 5px 20px;
  color: #bfc9d4;
}
body.dark .scrumboard .card.task-text-progress .card-body .task-content .progress {
  height: 9px;
  width: 100%;
  margin-right: 17px;
  margin-bottom: 0;
  align-self: center;
  background: #0e1726;
}
body.dark .scrumboard .card.task-text-progress .card-body .task-content .progress .progress-bar {
  background-color: #009688 !important;
  border-color: #009688;
}
body.dark .scrumboard .card.task-text-progress .card-body .task-content > div {
  display: flex;
  padding: 5px 20px 5px 20px;
}
body.dark .scrumboard .card.task-text-progress .card-body .task-content > div p.progress-count {
  padding: 0;
  margin-bottom: 0;
}
body.dark .scrumboard .card.ui-sortable-helper {
  background-color: #009688;
  background: rgba(0, 150, 136, 0.28);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
body.dark .scrumboard .card.ui-sortable-helper .task-header span {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper .task-header span svg {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper .task-header svg.feather-edit-2, body.dark .scrumboard .card.ui-sortable-helper .task-header svg.feather-trash-2 {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper .task-header h4 {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper.task-text-progress .card-body .task-content p {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper.task-text-progress .card-body .task-content .progress .progress-bar {
  background-color: #2196f3 !important;
}
body.dark .scrumboard .card.ui-sortable-helper .task-header svg.feather-user {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper .card-body .task-body .task-bottom div.tb-section-1 {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper .card-body .task-body .task-bottom div.tb-section-1 svg {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper .card-body .task-body .task-bottom div.tb-section-2 svg {
  color: #fff;
}
body.dark .scrumboard .card.ui-sortable-helper .card-body .task-content .progress {
  box-shadow: none;
}

/*
    img task
*/
/*
    task-text-progress
*/
/*
    Style On events
*/
/* On Drag Task */
body.dark .ui-state-highlight {
  position: relative;
  border-color: #009688;
  height: 141px;
  margin-bottom: 36px;
  border-radius: 15px;
  border: 1px dashed #009688;
  background-image: linear-gradient(45deg, rgba(27, 85, 226, 0.09) 25%, transparent 25%, transparent 50%, rgba(27, 85, 226, 0.09) 50%, rgba(27, 85, 226, 0.09) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
  animation: progress-bar-stripes 1s linear infinite;
}
body.dark .ui-state-highlight:before {
  content: "Drop";
  position: absolute;
  left: 41%;
  font-size: 19px;
  color: #009688;
  top: 50%;
  margin-top: -16px;
  font-weight: 600;
}
body.dark .connect-sorting-content {
  min-height: 60px;
}
@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }
  100% {
    background-position: 0 0;
  }
}/*# sourceMappingURL=scrumboard.css.map */