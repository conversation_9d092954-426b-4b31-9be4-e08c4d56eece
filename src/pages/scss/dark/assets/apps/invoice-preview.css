/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .actions-btn-tooltip.tooltip {
  opacity: 1;
  top: -11px !important;
}
body.dark .actions-btn-tooltip .arrow:before {
  border-top-color: #3b3f5c;
}
body.dark .actions-btn-tooltip .tooltip-inner {
  background: #3b3f5c;
  color: #fff;
  font-weight: 700;
  border-radius: 30px;
  box-shadow: 0px 5px 15px 1px rgba(113, 106, 202, 0.2);
  padding: 4px 16px;
}
body.dark .invoice-container {
  width: 100%;
}
body.dark .invoice-inbox {
  padding: 0;
  background-color: #0e1726;
  box-shadow: 0 0 40px 0 rgba(94, 92, 154, 0.06);
  border-radius: 8px;
  border: 1px solid #0e1726;
}
body.dark .invoice-inbox .inv-number {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
  color: #888ea8;
}
body.dark .invoice-inbox .invoice-action svg {
  cursor: pointer;
  font-weight: 600;
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  fill: rgba(0, 23, 55, 0.08);
}
body.dark .invoice-inbox .invoice-action svg:not(:last-child) {
  margin-right: 15px;
}
body.dark .invoice-inbox .invoice-action svg:hover {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.2392156863);
}

/*
===================

     Invoice

===================
*/
/*    Inv head section   */
body.dark .invoice .content-section .inv--head-section {
  padding: 36px 35px;
  margin-bottom: 40px;
  padding-bottom: 25px;
  border-bottom: 1px solid #191e3a;
}
body.dark .inv--customer-detail-section {
  padding: 36px 35px;
  padding-top: 0;
}
body.dark .invoice .content-section .inv--head-section h3.in-heading {
  font-size: 18px;
  font-weight: 600;
  color: #e0e6ed;
  margin: 0;
  margin-left: 12px;
}
body.dark .invoice .content-section .inv--head-section .company-logo {
  width: 70px;
  height: 70px;
}
body.dark .invoice .content-section .inv--head-section div.company-info {
  display: flex;
  justify-content: flex-end;
}
body.dark .invoice .content-section .inv--head-section div.company-info svg {
  width: 42px;
  height: 42px;
  margin-right: 10px;
  color: #61b6cd;
  fill: rgba(27, 85, 226, 0.2392156863);
}
body.dark .invoice .content-section .inv--head-section .inv-brand-name {
  font-size: 23px;
  font-weight: 600;
  margin-bottom: 0;
  align-self: center;
}
body.dark .invoice .content-section .inv--detail-section .inv-to {
  font-weight: 700;
  font-size: 15px;
  margin-bottom: 15px;
}
body.dark .invoice .content-section .inv--detail-section .inv-customer-name {
  font-weight: 600;
  margin-bottom: 2px;
  font-size: 15px;
  color: #61b6cd;
}
body.dark .invoice .content-section .inv--detail-section .inv-detail-title {
  font-weight: 700;
  margin-bottom: 0;
  font-size: 15px;
  margin-bottom: 15px;
}
body.dark .invoice .content-section .inv--detail-section .inv-details {
  font-weight: 700;
  margin-bottom: 15px;
}
body.dark .invoice .content-section .inv--detail-section .inv-street-addr {
  /* font-weight: 600; */
  margin-bottom: 2px;
  font-size: 15px;
  color: #888ea8;
}
body.dark .invoice .content-section .inv--detail-section .inv-email-address {
  font-weight: 600;
  margin-bottom: 2px;
  font-size: 15px;
  color: #888ea8;
}
body.dark .invoice .content-section .inv--detail-section .inv-list-number {
  margin-bottom: 2px;
}
body.dark .invoice .content-section .inv--detail-section .inv-list-number .inv-title {
  font-weight: 400;
  font-size: 20px;
}
body.dark .invoice .content-section .inv--detail-section .inv-list-number .inv-number {
  font-weight: 400;
  font-size: 18px;
  color: #61b6cd;
}
body.dark .invoice .content-section .inv--detail-section .inv-created-date, body.dark .invoice .content-section .inv--detail-section .inv-due-date {
  margin-bottom: 2px;
  color: #888ea8;
}
body.dark .invoice .content-section .inv--detail-section .inv-created-date .inv-title, body.dark .invoice .content-section .inv--detail-section .inv-due-date .inv-title {
  font-weight: 700;
  font-size: 15px;
}
body.dark .invoice .content-section .inv--detail-section .inv-created-date .inv-date, body.dark .invoice .content-section .inv--detail-section .inv-due-date .inv-date {
  font-size: 15px;
  font-weight: 600;
}
body.dark .invoice .content-section .inv--product-table-section {
  padding: 30px 0;
}
body.dark .invoice .content-section .inv--product-table-section table {
  margin-bottom: 0;
}
body.dark .invoice .content-section .inv--product-table-section thead tr {
  border: none;
}
body.dark .invoice .content-section .inv--product-table-section th {
  padding: 9px 22px;
  font-size: 13px !important;
  border: none;
  color: #bfc9d4 !important;
}
body.dark .invoice .content-section .inv--product-table-section th:first-child {
  padding-left: 35px;
  border-radius: 0;
}
body.dark .invoice .content-section .inv--product-table-section th:last-child {
  padding-right: 35px;
  border-radius: 0;
}
body.dark .invoice .content-section .inv--product-table-section tr td:first-child {
  padding-left: 35px;
}
body.dark .invoice .content-section .inv--product-table-section tr td:last-child {
  padding-right: 35px;
}
body.dark .invoice .content-section .inv--product-table-section td {
  color: #888ea8;
  border: none;
  padding: 10px 25px;
  vertical-align: top !important;
  font-size: 15px;
}
body.dark .invoice .content-section .inv--product-table-section tbody tr {
  border-bottom: 1px solid #191e3a;
}
body.dark .invoice .content-section .inv--product-table-section tbody tr:nth-of-type(even) td {
  background-color: rgba(3, 3, 5, 0.122) !important;
}
body.dark .invoice .content-section .inv--payment-info {
  font-size: 15px;
  font-weight: 600;
}
body.dark .invoice .content-section .inv--payment-info .inv-title {
  color: #61b6cd;
  font-weight: 600;
  margin-bottom: 15px;
  width: 65%;
  margin-left: auto;
}
body.dark .invoice .content-section .inv--payment-info p {
  margin-bottom: 0;
  display: flex;
  width: 65%;
  margin-left: auto;
  justify-content: space-between;
}
body.dark .invoice .content-section .inv--payment-info span {
  font-weight: 500;
  display: inline-block;
  color: #888ea8;
  white-space: nowrap;
}
body.dark .invoice .content-section .inv--payment-info .inv-subtitle {
  font-weight: 600;
  font-size: 15px;
  display: inline-block;
  color: #888ea8;
  white-space: normal;
  margin-right: 4px;
}
body.dark .invoice .content-section .inv--total-amounts {
  padding: 0 35px;
  margin-bottom: 25px;
  padding-bottom: 25px;
  border-bottom: 1px solid #191e3a;
}
body.dark .invoice .content-section .inv--total-amounts .grand-total-title h4, body.dark .invoice .content-section .inv--total-amounts .grand-total-amount h4 {
  position: relative;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 0;
  padding: 0;
  color: #fff;
  display: inline-block;
  letter-spacing: 1px;
}

/*    Inv detail section    */
/*inv-list-number*/
/*inv-created-date*/
/*inv-due-date*/
/*    Inv product table section    */
/*inv--payment-info*/
/*inv--total-amounts*/
/*inv--note*/
body.dark .inv--note {
  padding: 0 25px;
  padding-bottom: 25px;
}
body.dark .inv--note p {
  margin-bottom: 0;
  font-weight: 600;
  color: #888ea8;
}
@media print {
  body.dark body * {
    visibility: hidden;
  }
  body.dark #ct {
    visibility: visible;
  }
  body.dark #ct * {
    visibility: visible;
  }
  body.dark .doc-container {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
  }
}
@page {
  body.dark {
    size: auto;
    margin: 0mm;
  }
}

/*
===============================
    Invoice Actions Button
===============================
*/
body.dark .invoice-actions-btn {
  padding: 25px;
  padding-top: 32px;
  padding-bottom: 32px;
  background-color: #0e1726;
  border-radius: 8px;
  border: 1px solid #0e1726;
}
body.dark .invoice-actions-btn label {
  font-size: 14px;
  font-weight: 600;
  color: #515365;
}
body.dark .invoice-actions-btn .invoice-action-btn a {
  transform: none;
}
body.dark .invoice-actions-btn .invoice-action-btn a.btn-send, body.dark .invoice-actions-btn .invoice-action-btn a.btn-print, body.dark .invoice-actions-btn .invoice-action-btn a.btn-download {
  width: 100%;
  margin-bottom: 20px;
}
body.dark .invoice-actions-btn .invoice-action-btn a.btn-edit {
  width: 100%;
}

/* Invoice Actions -> action-btn */
@media (max-width: 1199px) {
  body.dark .invoice-actions-btn {
    margin-top: 25px;
  }
  body.dark .invoice-actions-btn .invoice-action-btn a.btn-send, body.dark .invoice-actions-btn .invoice-action-btn a.btn-print, body.dark .invoice-actions-btn .invoice-action-btn a.btn-download {
    margin-bottom: 0;
  }
}
@media (max-width: 767px) {
  body.dark .invoice-actions-btn .invoice-action-btn a.btn-send, body.dark .invoice-actions-btn .invoice-action-btn a.btn-print {
    margin-bottom: 20px;
  }
}
@media (max-width: 575px) {
  body.dark .invoice .content-section .inv--payment-info .inv-title {
    margin-top: 25px;
    margin-left: 0;
    margin-right: auto;
    margin-bottom: 6px;
    width: auto;
  }
  body.dark .invoice .content-section .inv--payment-info p {
    margin-left: 0;
    margin-right: auto;
    width: auto;
    justify-content: flex-start;
  }
  body.dark .invoice .content-section .inv--payment-info .inv-subtitle {
    min-width: 140px;
  }
  body.dark .invoice-actions-btn .invoice-action-btn a.btn-download {
    margin-bottom: 20px;
  }
  body.dark .invoice .content-section .inv--payment-info span {
    white-space: normal;
  }
}/*# sourceMappingURL=invoice-preview.css.map */