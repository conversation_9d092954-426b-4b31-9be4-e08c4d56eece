/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .widget-content-area, body.dark .dataTables_wrapper {
  padding: 0;
}
body.dark div.dataTables_wrapper button:hover {
  transform: none;
}
body.dark .inv-list-top-section {
  margin: 20px 21px 20px 21px;
}
body.dark div.dataTables_wrapper div.dataTables_length {
  align-self: center;
}
body.dark div.dataTables_wrapper div.dataTables_length label {
  margin-bottom: 0;
  margin-right: 15px;
}
body.dark .dataTables_wrapper .dataTables_length select.form-control {
  margin: 0;
}
body.dark div.dataTables_wrapper div.dataTables_filter {
  align-self: center;
}
body.dark div.dataTables_wrapper div.dataTables_filter svg {
  top: 10px;
}
body.dark div.dataTables_wrapper div.dataTables_filter label {
  margin: 0;
  margin-right: 15px;
}
body.dark div.dataTables_wrapper div.dataTables_filter input {
  margin: 0;
}
body.dark .table-responsive {
  overflow-x: auto;
  overflow-y: hidden;
}
body.dark table.dataTable {
  margin: 0 !important;
}
body.dark .table > thead {
  border-top: none;
  border-bottom: none;
}
body.dark .table > thead > tr > th {
  text-transform: initial;
  font-weight: 600;
  border-top: none;
  border-right: none;
  border-left: none;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
  transition: all 0.1s ease;
  padding: 10px 21px 10px 21px;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 1px;
  white-space: nowrap;
}
body.dark .table > thead > tr > th:first-child:before, body.dark .table > thead > tr > th:first-child:after {
  display: none;
}
body.dark .table > thead > tr > th:last-child:before, body.dark .table > thead > tr > th:last-child:after {
  display: none;
}
body.dark .table > tbody:before {
  display: none;
}
body.dark .table > tbody > tr > td {
  padding: 0;
  padding: 10px 21px 10px 21px;
  letter-spacing: normal;
  white-space: nowrap;
}
body.dark .table > tbody > tr > td:first-child {
  border-top-left-radius: 8px;
}
body.dark .table > tbody > tr > td .inv-number {
  color: #61b6cd;
  cursor: pointer;
  font-size: 16px;
  text-align: left;
}
body.dark .table > tbody > tr > td .user-name {
  color: #d3d3d3;
  font-size: 14px;
  letter-spacing: 0.14px;
  margin-bottom: 0;
  overflow: hidden;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: break-word;
}
body.dark .table > tbody > tr > td .inv-email {
  color: #888ea8;
  font-size: 14px;
  letter-spacing: 0.14px;
  margin-bottom: 0;
  margin-top: 0;
  overflow: hidden;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: break-word;
}
body.dark .table > tbody > tr > td .inv-email svg {
  width: 17px;
  height: 17px;
  vertical-align: text-bottom;
  color: #805dca;
  stroke-width: 1.5;
}
body.dark .table > tbody > tr > td .inv-date svg {
  width: 17px;
  height: 17px;
  vertical-align: text-top;
  color: #2196f3;
  stroke-width: 1.5;
}
body.dark .table > tbody > tr > td .dropdown .dropdown-toggle svg {
  stroke-width: 1px;
}
body.dark .table > tbody > tr > td .dropdown.show .dropdown-toggle svg {
  stroke-width: 1px;
  color: #7367f0;
}
body.dark .table > tbody > tr > td .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  padding: 9px !important;
}
body.dark .table > tbody > tr:last-child > td .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu, body.dark .table > tbody > tr:nth-last-child(2) > td .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  top: -94px !important;
}
body.dark .table > tbody > tr:last-child > td .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.show, body.dark .table > tbody > tr:nth-last-child(2) > td .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.show {
  top: -90px !important;
}
body.dark .table > tbody > tr > td .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item svg {
  width: 16px;
  height: 16px;
  margin-right: 7px;
  vertical-align: text-top;
}

/* 
    Inv List Bottom Section
*/
body.dark .inv-list-bottom-section {
  padding: 15px;
}/*# sourceMappingURL=invoice-list.css.map */