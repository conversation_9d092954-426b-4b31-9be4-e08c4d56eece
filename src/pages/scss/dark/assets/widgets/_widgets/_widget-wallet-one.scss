@import '../../base/base';
  /*
      =====================
          Account Info
      =====================
  */
  body.dark {
    .widget {
      &.widget-wallet-one {
        .wallet-title {
          letter-spacing: 0px;
          font-size: 18px;
          display: block;
          color: #e0e6ed;
          font-weight: 600;
          margin-bottom: 0;
        }
      
        .total-amount {
          font-size: 38px;
          color: #888ea8;
          font-weight: 600;
        }
      
        .wallet-text {
          color: #d3d3d3;
          letter-spacing: 2px;
      
          &:hover {
            color: #22c7d5;
          }
      
          svg {
            width: 16px;
            height: 16px;
          }
        }
      
        .wallet-action {
          padding: 4px 0px;
          border-radius: 10px;
          max-width: 350px;
          margin: 0 auto;
        }
      
        .list-group {
          .list-group-item {
            border: none;
            padding-left: 0;
            padding-right: 0;
            position: relative;
          }
      
          &.list-group-media .list-group-item .media .media-body h6 {
            color: #e0e6ed;
            font-weight: 500;
          }
      
          .list-group-item .amount {
            position: absolute;
            top: 21px;
            right: 0;
          }
        }
      }
    }
  }