/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .section {
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .layout-spacing {
  padding-bottom: 25px;
}
body.dark .general-info .info h6, body.dark .social .info h5 {
  color: #bfc9d4;
  margin: 5px 0 40px 0;
  font-weight: 600;
  font-size: 18px;
  letter-spacing: 1px;
}
body.dark .animated-underline-content .nav-tabs li a {
  font-size: 15px;
  letter-spacing: 1px;
}
body.dark .animated-underline-content .nav-tabs .nav-link.active, body.dark .animated-underline-content .nav-tabs .show > .nav-link {
  background-color: transparent;
  color: #00ab55;
}
body.dark .animated-underline-content .nav-tabs .nav-link.active:hover svg, body.dark .animated-underline-content .nav-tabs .show > .nav-link:hover svg, body.dark .animated-underline-content .nav-tabs .nav-link.active:hover, body.dark .animated-underline-content .nav-tabs .show > .nav-link:hover {
  color: #bfc9d4;
}
body.dark .animated-underline-content .nav-tabs .nav-link:before {
  background-color: #00ab55;
}

/*
    General Infomation
*/
body.dark .general-info {
  background-color: #0e1726;
  border-radius: 6px;
  border: 1px solid #0e1726;
}
body.dark .general-info .info, body.dark .general-info .save-info {
  padding: 20px;
}
body.dark .general-info .info .upload {
  border-right: 1px solid #191e3a;
}
body.dark .general-info .info .upload p {
  font-size: 14px;
  font-weight: 600;
  color: #009688;
}
body.dark .general-info .info label {
  color: #888ea8;
  letter-spacing: 1px;
}

/*
    Social
*/
body.dark .social {
  background-color: #0e1726;
  border-radius: 6px;
  border: 1px solid #0e1726;
}
body.dark .social .info, body.dark .social .save-info {
  padding: 20px;
}
body.dark .social .input-group-text {
  border-radius: 6px !important;
  color: #fff;
  border: none;
}
body.dark .input-group .input-group-text svg, body.dark .input-group:hover .input-group-text svg {
  color: #009688;
}
body.dark .social .info input {
  border-radius: 0.25rem !important;
}

/*
    Payment Methods
*/
body.dark .payment-info .list-group-item {
  border: none;
  border-bottom: 1px solid #1b2e4b;
  padding-left: 0;
  padding-right: 0;
}
body.dark .payment-info .list-group-item:first-child {
  border-bottom: 1px solid #1b2e4b;
}
body.dark .payment-info .list-group-item:last-child {
  border: none;
}
body.dark .payment-info .list-group-item .billing-content p {
  color: #bfc9d4;
  font-size: 12px;
}

/*
    Invoice
*/
body.dark .invoice-action-currency label {
  padding: 0 25px 10px 25px;
  padding-bottom: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #191e3a;
  width: 100%;
  font-size: 16px;
  color: #e0e6ed;
  font-weight: 500;
}
body.dark .invoice-action-currency a.dropdown-toggle {
  padding: 9px 38px 9px 45px;
  width: 100%;
}
body.dark .invoice-action-currency a.dropdown-toggle span {
  vertical-align: middle;
}
body.dark .invoice-action-currency .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  width: 100%;
  padding: 6px 15px;
}
body.dark .invoice-action-currency .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu .dropdown-item {
  padding: 10px 3px;
  border-radius: 0;
  font-size: 16px;
  line-height: 1.45;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
body.dark .invoice-action-currency .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu img {
  vertical-align: sub;
}
body.dark .selectable-dropdown a.dropdown-toggle {
  padding: 11px 35px 10px 15px;
  position: relative;
  padding: 12px 20px 12px 44px;
  border-radius: 6px;
  transform: none;
  font-size: 15px;
  background-color: #1b2e4b;
  letter-spacing: normal;
  text-align: inherit;
  color: #bfc9d4;
  box-shadow: none;
  display: inline-block;
  cursor: pointer;
  width: 100%;
  border: 1px solid #1b2e4b;
}
body.dark .selectable-dropdown a.dropdown-toggle img {
  width: 24px;
  height: 24px;
  vertical-align: text-bottom;
  position: absolute;
  left: 12px;
  top: 10px;
}
body.dark .selectable-dropdown a.dropdown-toggle .selectable-text {
  overflow: hidden;
  display: block;
}
body.dark .selectable-dropdown a.dropdown-toggle .selectable-arrow {
  display: inline-block;
  position: absolute;
  padding: 6px 4px;
  background: #1b2e4b;
  top: 6px;
  right: 3px;
}
body.dark .selectable-dropdown a.dropdown-toggle svg {
  color: #009688;
  width: 15px !important;
  height: 15px !important;
  margin: 0;
  transition: transform 0.2s ease-in-out;
}
body.dark .selectable-dropdown a.dropdown-toggle.show svg {
  transform: rotate(180deg);
}
body.dark .selectable-dropdown.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  right: auto;
  top: 65px !important;
}
body.dark .selectable-dropdown.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.show {
  top: 50px !important;
}
body.dark .selectable-dropdown.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu img {
  width: 30px;
  height: 30px;
  margin-right: 7px;
  vertical-align: top;
  background: #0e1726;
  padding: 4px 4px;
  border-radius: 6px;
}/*# sourceMappingURL=account-setting.css.map */