/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*      Alert       */
body.dark .alert {
  border-radius: 5px;
  margin-bottom: 15px;
  padding: 0.9375rem;
}
body.dark .alert .btn {
  margin-right: 27px;
}
body.dark .alert .btn:hover {
  box-shadow: none;
}
body.dark .alert .alert-icon svg {
  vertical-align: middle;
  width: 33px;
  height: 33px;
  stroke-width: 1.2;
}
body.dark .alert .btn-close {
  color: #fff;
  opacity: 1;
  width: 18px;
  background: transparent;
  padding: 13px 12px;
  box-shadow: none;
}
body.dark .alert .btn-close svg {
  width: 18px;
  height: 18px;
}

/*Default Alerts*/
body.dark .alert-primary {
  color: #fff;
  background-color: #4361ee;
  border-color: #4361ee;
}
body.dark .alert-warning {
  color: #fff;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .alert-success {
  color: #fff;
  background-color: #00ab55;
  border-color: #00ab55;
}
body.dark .alert-info {
  color: #fff;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .alert-danger {
  color: #fff;
  background-color: #e7515a;
  border-color: #e7515a;
}
body.dark .alert-dark {
  color: #fff;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

/*Outline Alerts*/
body.dark .alert-outline-primary {
  border-color: #4361ee;
  border-radius: 5px;
}
body.dark .alert-outline-warning {
  border-color: #dea82a;
  border-radius: 5px;
}
body.dark .alert-outline-success {
  border-color: #00ab55;
  border-radius: 5px;
}
body.dark .alert-outline-info {
  border-color: #009eda;
  border-radius: 5px;
}
body.dark .alert-outline-danger {
  border-color: #e7515a;
  border-radius: 5px;
}
body.dark .alert-outline-dark {
  border-color: #454656;
  border-radius: 5px;
}
body.dark .alert.alert-light .close {
  color: #0e1726;
}
body.dark .alert.solid-alert-3 .close, body.dark .alert.solid-alert-4 .close {
  color: #000;
}
body.dark .hide-default {
  display: none;
}

/*      Light Alert         */
body.dark .btn-light {
  border-color: transparent;
}
body.dark .alert-light-primary {
  color: #4361ee;
  background-color: #152143;
  border-color: rgba(67, 97, 238, 0.55);
}
body.dark .alert-light-primary svg.close {
  color: #4361ee;
}
body.dark .alert-light-warning {
  color: #e2a03f;
  background-color: #282625;
  border-color: rgba(226, 160, 63, 0.55);
}
body.dark .alert-light-warning svg.close {
  color: #e2a03f;
}
body.dark .alert-light-success {
  color: #00ab55;
  background-color: #0c272b;
  border-color: rgba(26, 188, 156, 0.55);
}
body.dark .alert-light-success svg.close {
  color: #00ab55;
}
body.dark .alert-light-info {
  color: #2196f3;
  background-color: #0b2f52;
  border-color: rgba(33, 150, 243, 0.55);
}
body.dark .alert-light-info svg.close {
  color: #2196f3;
}
body.dark .alert-light-danger {
  color: #e7515a;
  background-color: #2c1c2b;
  border-color: rgba(231, 81, 90, 0.55);
}
body.dark .alert-light-danger svg.close {
  color: #e7515a;
}
body.dark .alert-light-dark {
  color: #bfc9d4;
  background-color: #181e2e;
  border-color: rgba(59, 63, 92, 0.55);
}
body.dark .alert-light-dark svg.close {
  color: #3b3f5c;
}
body.dark .alert-light-dark svg:not(.close) {
  color: #3b3f5c !important;
}

/*  Background Alerts      */
body.dark .alert-background {
  color: #fff;
  background: #fff url(../../../img/ab-1.jpeg) no-repeat center center;
  background-size: cover;
  border: none;
}

/*  Gradient Alerts      */
body.dark .alert-gradient {
  color: #fff;
  border: none;
  background-size: cover;
  background-image: linear-gradient(135deg, #bc1a4e 0%, #004fe6 100%);
}

/* Custom Alerts */
/* Default */
body.dark .custom-alert-1 {
  background-color: rgba(0, 171, 85, 0.4196078431);
  border-radius: 5px;
  color: #fff;
}
body.dark .custom-alert-1 .btn-close {
  top: 9px;
}
body.dark .custom-alert-1 .alert-icon {
  margin-right: 25px;
}
body.dark .custom-alert-1 .media-body {
  display: flex;
  justify-content: space-between;
}
body.dark .custom-alert-1 .alert-text {
  margin-right: 10px;
}
body.dark .custom-alert-1 .alert-text strong, body.dark .custom-alert-1 .alert-text span {
  vertical-align: sub;
}

/*  Alert with Icon */
body.dark .alert-icon-left {
  border-left: 64px solid;
}
body.dark .alert-icon-left svg:not(.close) {
  color: #FFF;
  width: 4rem;
  left: -4rem;
  text-align: center;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body.dark .alert-icon-right {
  border-right: 64px solid;
}
body.dark .alert-icon-right svg:not(.close) {
  color: #FFF;
  width: 4rem;
  right: -4rem;
  text-align: center;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body.dark .alert-icon-right i {
  float: left;
  margin-right: 7px;
}
body.dark .alert[class*=alert-arrow-]:before {
  content: "";
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  border-left: 8px solid;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left-color: inherit;
  margin-top: -8px;
}
body.dark .alert.alert-arrow-right:before {
  left: auto;
  right: 0;
  border-left: 0;
  border-right: 8px solid;
  border-right-color: inherit;
}

@media (max-width: 575px) {
  body.dark .custom-alert-1 .media-body {
    display: block;
  }
  body.dark .alert .btn {
    margin-top: 8px;
  }
}/*# sourceMappingURL=alert.css.map */