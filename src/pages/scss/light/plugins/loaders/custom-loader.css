/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.element-loader.loader {
  border: 7px solid #f1f2f3;
  border-radius: 50%;
  border-top: 7px solid #4361ee;
  width: 58px;
  height: 58px;
  /* Safari */
  animation: spin 2s linear infinite;
}
.element-loader.loader.dual-loader {
  border-bottom: 7px solid #4361ee;
}
.element-loader.loader.multi-loader {
  border-bottom: 7px solid #e2a03f;
  border-right: 7px solid #00ab55;
  border-left: 7px solid #e7515a;
}

/* Safari */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
.spinner-border {
  position: relative;
  display: inline-block;
  width: 2rem;
  height: 2rem;
  overflow: hidden;
  text-indent: -999em;
  border: 0.25em solid;
  border-color: currentColor transparent currentColor currentColor;
  border-radius: 50%;
  animation-name: spinner-border;
  animation-duration: 0.75s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  vertical-align: bottom;
}

.spinner-border-reverse {
  border-color: transparent currentColor transparent transparent;
}
.spinner-border-reverse::after {
  position: absolute;
  top: -0.25em;
  right: -0.25em;
  bottom: -0.25em;
  left: -0.25em;
  display: inline-block;
  content: "";
  border: 0.25em solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}
@keyframes spinner-grow {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}
.spinner-grow {
  position: relative;
  display: inline-block;
  width: 2rem;
  height: 2rem;
  overflow: hidden;
  text-indent: -999em;
  background-color: currentColor;
  border-radius: 50%;
  animation-name: spinner-grow;
  animation-duration: 0.75s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  vertical-align: bottom;
}

.loader-lg {
  width: 2.5rem;
  height: 2.5rem;
  border-width: 0.35em;
}

.loader-sm {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 0.15em;
}/*# sourceMappingURL=custom-loader.css.map */