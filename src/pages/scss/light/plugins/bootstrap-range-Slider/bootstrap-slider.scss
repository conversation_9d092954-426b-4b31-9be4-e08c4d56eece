@import '../../base/base';
.custom-progress {
  &.progress-up .range-count {
    margin-bottom: 15px;
  }

  &.progress-down .range-count {
    margin-top: 15px;
  }
}

.range-count {
  font-weight: 700;
  color: $dark;

  .range-count-number {
    display: inline-block;
    background: #fff;
    padding: 3px 8px;
    border-radius: 5px;
    color: $primary;
    border: 1px solid #e0e6ed;
  }

  .range-count-unit {
    color: $primary;
  }
}

.custom-progress {
  &.top-right .range-count, &.bottom-right .range-count {
    text-align: right;
  }
}

.progress-range-counter {
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: $primary;
    cursor: pointer;
    height: 16px;
    width: 16px;
    margin-top: -4px;
    -webkit-transition: all 0.35s ease;
    transition: all 0.35s ease;
  }

  &:active::-webkit-slider-thumb {
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
  }

  &:focus::-webkit-slider-thumb {
    background: $primary;
    cursor: pointer;
    height: 16px;
    width: 16px;
    margin-top: -4px;
    box-shadow: none;
  }

  &::-moz-range-thumb {
    background: $primary;
    cursor: pointer;
    height: 16px;
    width: 16px;
    margin-top: -4px;
  }
}

input[type=range] {
  &::-webkit-slider-runnable-track, &::-moz-range-track, &::-ms-track {
    background: #191e3a;
  }
}