/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.noUi-target {
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e0e6ed;
  box-shadow: none;
}

.noUi-horizontal {
  height: 8px;
}
.noUi-horizontal .noUi-handle {
  width: 25px;
  height: 20px;
  top: -8px;
  border: 1px solid #e0e6ed;
  border-radius: 3px;
  background: #fff;
  cursor: default;
  box-shadow: inset 0 0 1px #fff, inset 0 1px 7px #ebebeb, 0 3px 6px -3px #bbb;
}

.noUi-handle:after, .noUi-handle:before {
  display: none;
}

.noUi-connect {
  background: #4361ee;
}

.noUi-tooltip {
  border: 1px solid #e0e6ed;
  border-radius: 8px;
  background: #fff;
  color: #000;
  padding: 6px 14px;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 0 40px 0 rgba(94, 92, 154, 0.06);
}

.noUi-horizontal .noUi-tooltip {
  bottom: 148%;
}

.example-val {
  font-weight: 700;
  font-size: 14px;
  color: #4361ee;
}
.example-val span.precentage-val {
  display: inline-block;
  background: #fff;
  border-radius: 5px;
  color: #515365;
  border: 1px solid #e0e6ed;
  padding: 4px 6px;
  font-size: 14px;
}/*# sourceMappingURL=custom-nouiSlider.css.map */