/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.widget .widget-content {
  padding: 45px;
  border-radius: 8px;
}

.contact-us-form .paper {
  margin-top: 36px;
  border-radius: 8px;
}
.contact-us-form .paper .contact-title {
  margin-bottom: 44px;
  border-bottom: 1px solid #3d5df3;
  display: inline-block;
}
.contact-us-form .paper .widget-paper {
  background-color: #f1f2f3;
  padding: 30px 22px;
  border-radius: 8px;
  text-align: center;
}
.contact-us-form .paper .widget-paper .icon {
  margin-bottom: 10px;
}
.contact-us-form .paper .widget-paper .icon svg {
  width: 50px;
  height: 50px;
  stroke-width: 1px;
  color: #3d5df3;
}
.contact-us-form .paper .widget-paper h5 {
  font-weight: 500;
}
.contact-us-form .paper .widget-paper p {
  font-size: 17px;
  margin-bottom: 0;
}

@media (max-width: 575px) {
  .widget .widget-content {
    padding: 25px 25px;
  }
}/*# sourceMappingURL=contact_us.css.map */