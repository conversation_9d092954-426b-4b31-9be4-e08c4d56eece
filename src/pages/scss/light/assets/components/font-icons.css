/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.feather-icon .icon-section {
  padding: 30px;
}
.feather-icon .icon-section h4 {
  color: #3b3f5c;
  font-size: 17px;
  font-weight: 600;
  margin: 0;
  margin-bottom: 16px;
}
.feather-icon .icon-content-container {
  padding: 0 16px;
  width: 86%;
  margin: 0 auto;
  border: 1px solid #e0e6ed;
  border-radius: 6px;
}
.feather-icon .icon-section p.fs-text {
  padding-bottom: 30px;
  margin-bottom: 30px;
}
.feather-icon .icon-container {
  cursor: pointer;
}
.feather-icon .icon-container svg {
  color: #3b3f5c;
  margin-right: 6px;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  fill: none;
}
.feather-icon .icon-container:hover svg {
  color: #4361ee;
}
.feather-icon .icon-container span {
  display: none;
}
.feather-icon .icon-container:hover span {
  color: #888ea8;
}
.feather-icon .icon-link {
  color: #4361ee;
  font-weight: 600;
  font-size: 14px;
}

/*FAB*/
.fontawesome .icon-section {
  padding: 30px;
}
.fontawesome .icon-section h4 {
  color: #3b3f5c;
  font-size: 17px;
  font-weight: 600;
  margin: 0;
  margin-bottom: 16px;
}
.fontawesome .icon-content-container {
  padding: 0 16px;
  width: 86%;
  margin: 0 auto;
  border: 1px solid #e0e6ed;
  border-radius: 6px;
}
.fontawesome .icon-section p.fs-text {
  padding-bottom: 30px;
  margin-bottom: 30px;
}
.fontawesome .icon-container {
  cursor: pointer;
}
.fontawesome .icon-container i {
  font-size: 20px;
  color: #3b3f5c;
  vertical-align: middle;
  margin-right: 10px;
}
.fontawesome .icon-container:hover i {
  color: #4361ee;
}
.fontawesome .icon-container span {
  color: #888ea8;
  display: none;
}
.fontawesome .icon-container:hover span {
  color: #888ea8;
}
.fontawesome .icon-link {
  color: #4361ee;
  font-weight: 600;
  font-size: 14px;
}/*# sourceMappingURL=font-icons.css.map */