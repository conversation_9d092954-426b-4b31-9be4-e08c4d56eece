/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
h1, h2, h3, h4, h5, h6 {
  color: #e0e6ed;
}

/*
    Basic
*/
.accordion .card {
  border: 1px solid #d3d3d3;
  border-radius: 6px;
  margin-bottom: 4px;
  background: #fff;
}
.accordion .card-header {
  background-color: transparent;
  color: #f8538d;
  border-radius: 0;
  padding: 0;
  position: relative;
  border-bottom: none;
}
.accordion .card-header section > div {
  padding: 13px 19px;
  cursor: pointer;
  display: block;
  font-size: 14px;
  letter-spacing: 1px;
}
.accordion .card-header section > div.collapsed {
  color: #888ea8;
}
.accordion .card-header section > div:not(.collapsed) {
  color: #4361ee;
  border-bottom: 1px solid #d3d3d3;
  font-weight: 600;
}
.accordion .card-header section > div .icons {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 9px;
}
.accordion .card-header section > div .icons svg {
  width: 18px;
  transition: 0.5s;
  transform: rotate(0);
}
.accordion .card-header section > div[aria-expanded=true] .icons svg {
  transform: rotate(180deg);
}
.accordion .card .card-body p {
  color: #888ea8;
  letter-spacing: 1px;
  font-size: 13px;
}
.accordion .card .card-body p:not(:last-child) {
  margin-bottom: 10px;
}
.accordion .card .card-body ul {
  margin-bottom: 0;
}
.accordion .card .card-body ul li {
  font-size: 12px;
  letter-spacing: 1px;
}
.accordion .card .card-body ul li:not(:last-child) {
  margin-bottom: 5px;
}
.accordion .card .card-body ul li a {
  color: #3b3f5c;
  font-size: 13px;
  font-weight: 600;
}
.accordion .card .card-body ul li a:hover {
  color: #4361ee;
}
.accordion.no-outer-spacing {
  border: 1px solid #d3d3d3;
  border-radius: 6px;
}
.accordion.no-outer-spacing .card {
  margin-bottom: 0;
  border: none;
  border-radius: 0;
}
.accordion.no-outer-spacing .card:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.accordion.no-outer-spacing .card:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}
.accordion.no-outer-spacing .card:not(:last-child) {
  border-bottom: 1px solid #d3d3d3;
}
.accordion.no-outer-spacing .card-header section > div:not(.collapsed) {
  border-bottom: none;
}

/*
    No Outer Spacing
*/
/*
    Accordin with Icons
*/
.accordion-icons .accordion-icon {
  display: inline-block;
  margin-right: 10px;
}
.accordion-icons .accordion-icon svg {
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  fill: rgba(0, 23, 55, 0.08);
}
.accordion-icons div:not(.collapsed) .accordion-icon svg {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.07);
}/*# sourceMappingURL=accordions.css.map */