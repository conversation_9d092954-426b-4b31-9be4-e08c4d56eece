/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.list-group-item {
  border: 1px solid #e0e6ed;
  padding: 10px 12px;
  background-color: transparent;
  color: #3b3f5c;
  margin-bottom: 0;
}
.list-group-item .form-check-input:not(:checked) {
  background-color: #e0e6ed;
  border-color: #e0e6ed;
}
.list-group-item.active {
  color: #fff;
  background-color: #805dca;
  border-color: transparent;
  box-shadow: 0 1px 15px 1px rgba(52, 40, 104, 0.15);
}
.list-group-item.active:hover, .list-group-item.active:focus {
  color: #e0e6ed;
  background-color: #805dca;
  box-shadow: 0px 0px 12px 1px rgba(113, 106, 202, 0.08);
}
.list-group-item.disabled, .list-group-item:disabled {
  background: rgba(80, 102, 144, 0.1607843137);
  color: #888ea8;
}

.new-control-indicator {
  background-color: #f1f2f3;
}

a.list-group-item.list-group-item-action.active i {
  color: #010156;
}

code {
  color: #e7515a;
}

.list-group-item-action:hover {
  color: #060818;
  background-color: #f1f2f3;
}
.list-group-item-action:focus {
  background-color: transparent;
  color: #060818;
}

/*------list group-----*/
/*
    Icons Meta
*/
.list-group.list-group-icons-meta .list-group-item.active .media svg {
  font-size: 27px;
  color: #fff;
}
.list-group.list-group-icons-meta .list-group-item.active .media .media-body h6, .list-group.list-group-icons-meta .list-group-item.active .media .media-body p {
  color: #fff;
  font-weight: 500;
}
.list-group.list-group-icons-meta .list-group-item .media svg {
  width: 20px;
  color: #4361ee;
  height: 20px;
}
.list-group.list-group-icons-meta .list-group-item .media .media-body h6 {
  color: #3b3f5c;
  font-weight: 700;
  margin-bottom: 0;
  font-size: 15px;
  letter-spacing: 1px;
}
.list-group.list-group-icons-meta .list-group-item .media .media-body p {
  color: #888ea8;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
}
.list-group.list-group-media .list-group-item.active .media .media-body h6, .list-group.list-group-media .list-group-item.active .media .media-body p {
  color: #fff;
  font-weight: 500;
}
.list-group.list-group-media .list-group-item .media img {
  color: #4361ee;
  width: 42px;
  height: 42px;
}
.list-group.list-group-media .list-group-item .media .media-body {
  align-self: center;
}
.list-group.list-group-media .list-group-item .media .media-body h6 {
  color: #3b3f5c;
  font-weight: 700;
  margin-bottom: 0;
  font-size: 16px;
  letter-spacing: 1px;
}
.list-group.list-group-media .list-group-item .media .media-body p {
  color: #888ea8;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 600;
}
.list-group.task-list-group .list-group-item-action.active {
  background-color: #191e3a;
  color: #fff;
}
.list-group.task-list-group .list-group-item-action.active .new-control.new-checkbox {
  color: #fff;
  font-size: 14px;
}

/*
    Image Meta
*/
/*
    task-list-group
*//*# sourceMappingURL=list-group.css.map */