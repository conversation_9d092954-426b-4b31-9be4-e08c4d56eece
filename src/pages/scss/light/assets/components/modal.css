/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.modal-backdrop {
  background: linear-gradient(75deg, rgba(22, 28, 36, 0.48) 0%, rgb(22, 28, 36) 100%);
}
.modal-backdrop.show {
  opacity: 0.8;
}

.modal-content {
  border: none;
  border-radius: 6px;
  background: #fff;
  border-bottom: 1px solid #e0e6ed;
}
.modal-content hr {
  border-top: 1px solid #e0e6ed;
}
.modal-content .modal-header {
  padding: 12px 26px;
  border: none;
  border-bottom: 1px solid #e0e6ed;
}
.modal-content .modal-header h5 {
  font-weight: 600;
  font-size: 20px;
  letter-spacing: 1px;
}
.modal-content .modal-header svg {
  width: 17px;
  color: #bfc9d4;
}
.modal-content .modal-header .btn-close {
  background: none;
  box-shadow: none;
  padding: 0;
  margin: 0;
  display: grid;
  opacity: 1;
}
.modal-content .modal-header .btn-close svg {
  width: 17px;
  height: 17px;
  color: #000;
}
.modal-content .modal-body {
  padding: 26px 26px;
}
.modal-content .modal-body a:not(.btn) {
  color: #4361ee;
  font-weight: 600;
}
.modal-content .modal-body p {
  color: #888ea8;
  letter-spacing: 1px;
  font-size: 14px;
  line-height: 22px;
  text-align: left;
}
.modal-content .modal-body p:last-child {
  margin-bottom: 0;
}
.modal-content .modal-body p:not(:last-child) {
  margin-bottom: 10px;
}
.modal-content .modal-footer {
  border-top: 1px solid #e0e6ed;
}
.modal-content .modal-footer button.btn {
  font-weight: 600;
  padding: 10px 25px;
  letter-spacing: 1px;
}
.modal-content .modal-footer .btn.btn-primary {
  background-color: #4361ee;
  color: #fff;
  border: 1px solid #4361ee;
}

/*
    Modal Tabs
*/
.close {
  text-shadow: none;
  color: #bfc9d4;
}
.close:hover {
  color: #bfc9d4;
}

.nav-tabs {
  border-bottom: 1px solid #191e3a;
}
.nav-tabs svg {
  width: 20px;
  vertical-align: bottom;
}
.nav-tabs .nav-link.active {
  color: #e95f2b;
  background-color: #191e3a;
  border-color: #191e3a #191e3a #0e1726;
}
.nav-tabs .nav-link.active:after {
  color: #e95f2b;
}
.nav-tabs .nav-link:hover {
  border-color: #191e3a #191e3a #191e3a;
}

/*
    Modal Success
*/
.modal-success .modal-content {
  background-color: #ddf5f0;
}

/*
    Modal Video
*/
.modal-video .modal-content {
  background-color: transparent;
  border: none;
}
.modal-video .video-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}
.modal-video .modal#videoMedia1 .modal-header, .modal-video .modal#videoMedia2 .modal-header {
  border: none;
  padding: 12px 0;
  justify-content: end;
}
.modal-video .video-container iframe, .modal-video .video-container object, .modal-video .video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.modal-video .modal#videoMedia1 .modal-header .close, .modal-video .modal#videoMedia2 .modal-header .close {
  color: #fff !important;
  opacity: 1;
}
.modal-video .modal-content .modal-header svg {
  color: #fff;
}

/*
    Modal Notification
*/
.modal-notification .modal-body .icon-content {
  margin: 0 0 20px 0px;
  display: inline-block;
  padding: 13px;
  border-radius: 50%;
  background: #e0e6ed;
}
.modal-notification .modal-body .icon-content svg {
  width: 36px;
  height: 36px;
  color: #1b2e4b;
  fill: rgba(0, 23, 55, 0.08);
}

.modal#sliderModal .modal-content {
  border: none;
}
.modal#sliderModal .modal-content .modal-body button.btn-close {
  position: absolute;
  z-index: 2;
  right: 4px;
  top: -35px;
  opacity: 1;
  text-shadow: none;
  background: transparent;
  box-shadow: none;
}
.modal#sliderModal .modal-content .modal-body button.btn-close svg {
  color: #bfc9d4;
}
.modal#sliderModal .modal-content .modal-body button.btn-close:hover svg {
  color: #fff;
}

/*
    Form
*/
.inputForm-modal .modal-content .modal-body .form-group .input-group .input-group-text {
  background: transparent;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  border-right: none;
}
.inputForm-modal .modal-content .modal-body .form-group input {
  border-left: none;
  background: transparent;
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}
.inputForm-modal .modal-content .modal-body .form-group input:focus {
  border-color: #bfc9d4;
}/*# sourceMappingURL=modal.css.map */