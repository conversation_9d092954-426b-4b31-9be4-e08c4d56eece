/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.mt-container {
  max-width: 570px;
}

.modern-timeline {
  list-style: none;
  position: relative;
  padding: 50px 0 50px;
  margin: 0;
}
.modern-timeline:before {
  position: absolute;
  background: #ebedf2;
  bottom: 0;
  left: 50%;
  top: 0;
  content: "";
  width: 3px;
  margin-left: -1.5px;
}
.modern-timeline > li {
  margin-bottom: 50px;
  position: relative;
}
.modern-timeline > li:after, .modern-timeline > li:before {
  display: table;
  content: "";
}
.modern-timeline > li > .modern-timeline-badge {
  position: absolute;
  background: #fff;
  border: 3px solid #ebedf2;
  border-radius: 100%;
  height: 20px;
  width: 20px;
  margin-left: -10px;
  text-align: center;
  z-index: 1;
  left: 50%;
  top: 32px;
}
.modern-timeline > li > .modern-timeline-panel {
  position: relative;
  border: 1px solid #e0e6ed;
  background: #fff;
  border-radius: 0.1875rem;
  transition: 0.3s ease-in-out;
  float: left;
  width: 46%;
  border-radius: 6px;
}
.modern-timeline > li > .modern-timeline-panel:before {
  position: absolute;
  background: #ebedf2;
  right: -37px;
  top: 40px;
  transition: 0.3s ease-in-out;
  content: " ";
  width: 37px;
  height: 3px;
  display: block;
}
.modern-timeline > li:nth-child(even) > .modern-timeline-panel:before {
  right: auto;
  left: -37px;
  width: 37px;
}
.modern-timeline > li:after {
  clear: both;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-preview img {
  width: 100%;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.modern-timeline > li > .modern-timeline-panel *:last-child {
  margin-bottom: 0;
}
.modern-timeline > li:nth-child(even) > .modern-timeline-panel {
  border: 1px solid #e0e6ed;
  float: right;
}
.modern-timeline > li > .modern-timeline-panel *:last-child {
  margin-bottom: 0;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body {
  padding: 30px 20px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body h4 {
  color: #e95f2b;
  margin-bottom: 20px;
  font-size: 1.125rem;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body p {
  color: #888ea8;
  margin-bottom: 0;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body p a {
  display: block;
}
.modern-timeline > li > .modern-timeline-panel *:last-child {
  margin-bottom: 0;
}

.modern-timeline-top:before, .modern-timeline-bottom:before {
  background: #ebedf2;
  position: absolute;
  height: 3px;
  width: 50px;
  display: block;
  content: "";
  bottom: 0;
  left: 50%;
  margin-left: -25px;
}

.modern-timeline-top:before {
  top: 0;
}

@media (max-width: 767px) {
  ul.modern-timeline > li > .modern-timeline-panel {
    border: 1px solid #e0e6ed;
    float: right;
    width: 100%;
  }
  ul.modern-timeline > li > .modern-timeline-badge {
    display: none;
  }
  .modern-timeline > li > .modern-timeline-panel:before {
    display: none;
  }
}
/*
=====================
    Basic
=====================
*/
.timeline-line .item-timeline {
  display: flex;
}
.timeline-line .item-timeline .t-dot {
  position: relative;
}
.timeline-line .item-timeline .t-dot:before {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  border-color: #2196f3;
}
.timeline-line .item-timeline .t-dot:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  border-color: #2196f3;
  width: 0;
  height: auto;
  top: 25px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
}
.timeline-line .item-timeline .t-dot.t-dot-primary:before {
  border-color: #4361ee;
}
.timeline-line .item-timeline .t-dot.t-dot-success:before {
  border-color: #00ab55;
}
.timeline-line .item-timeline .t-dot.t-dot-warning:before {
  border-color: #e2a03f;
}
.timeline-line .item-timeline .t-dot.t-dot-info:before {
  border-color: #2196f3;
}
.timeline-line .item-timeline .t-dot.t-dot-danger:before {
  border-color: #e7515a;
}
.timeline-line .item-timeline .t-dot.t-dot-dark:before {
  border-color: #3b3f5c;
}
.timeline-line .item-timeline .t-dot.t-dot-primary:after {
  border-color: #4361ee;
}
.timeline-line .item-timeline .t-dot.t-dot-success:after {
  border-color: #00ab55;
}
.timeline-line .item-timeline .t-dot.t-dot-warning:after {
  border-color: #e2a03f;
}
.timeline-line .item-timeline .t-dot.t-dot-info:after {
  border-color: #2196f3;
}
.timeline-line .item-timeline .t-dot.t-dot-danger:after {
  border-color: #e7515a;
}
.timeline-line .item-timeline .t-dot.t-dot-dark:after {
  border-color: #3b3f5c;
}
.timeline-line .item-timeline:last-child .t-dot:after {
  display: none;
}
.timeline-line .item-timeline .t-meta-time {
  margin: 0;
  min-width: 100px;
  max-width: 100px;
  font-size: 12px;
  font-weight: 700;
  color: #888ea8;
  align-self: center;
}
.timeline-line .item-timeline .t-text {
  padding: 10px;
  align-self: center;
  margin-left: 10px;
}
.timeline-line .item-timeline .t-text p {
  font-size: 13px;
  margin: 0;
  color: #3b3f5c;
  font-weight: 600;
}
.timeline-line .item-timeline .t-text p a {
  color: #4361ee;
  font-weight: 600;
}
.timeline-line .item-timeline .t-time {
  margin: 0;
  min-width: 58px;
  max-width: 100px;
  font-size: 16px;
  font-weight: 600;
  color: #3b3f5c;
  padding: 10px 0;
}
.timeline-line .item-timeline .t-text .t-meta-time {
  margin: 0;
  min-width: 100px;
  max-width: 100px;
  font-size: 12px;
  font-weight: 700;
  color: #888ea8;
  align-self: center;
}

/*
=====================
    Modern
=====================
*/
.timeline-alter .item-timeline {
  display: flex;
}
.timeline-alter .item-timeline .t-time {
  padding: 10px;
  align-self: center;
}
.timeline-alter .item-timeline .t-time p {
  margin: 0;
  min-width: 58px;
  max-width: 100px;
  font-size: 16px;
  font-weight: 600;
  color: #3b3f5c;
  align-self: center;
}
.timeline-alter .item-timeline .t-img {
  position: relative;
  border-color: #e0e6ed;
  padding: 10px;
}
.timeline-alter .item-timeline .t-img:before {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}
.timeline-alter .item-timeline .t-img:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: auto;
  top: 25px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
}
.timeline-alter .item-timeline .t-img img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  z-index: 7;
  position: relative;
}
.timeline-alter .item-timeline .t-usr-txt {
  display: block;
  padding: 10px;
  position: relative;
  border-color: #e0e6ed;
}
.timeline-alter .item-timeline .t-usr-txt:before {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}
.timeline-alter .item-timeline .t-usr-txt:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: auto;
  top: 25px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
}
.timeline-alter .item-timeline .t-usr-txt p {
  margin: 0;
  background: #eceffe;
  height: 45px;
  width: 45px;
  border-radius: 50%;
  display: flex;
  align-self: center;
  justify-content: center;
  margin-bottom: 0;
  color: #4361ee;
  font-weight: 700;
  font-size: 18px;
  z-index: 7;
  position: relative;
}
.timeline-alter .item-timeline .t-usr-txt span {
  align-self: center;
}
.timeline-alter .item-timeline .t-meta-time {
  padding: 10px;
  align-self: center;
}
.timeline-alter .item-timeline .t-meta-time p {
  margin: 0;
  min-width: 100px;
  max-width: 100px;
  font-size: 12px;
  font-weight: 700;
  color: #888ea8;
}
.timeline-alter .item-timeline .t-text {
  padding: 10px;
  align-self: center;
}
.timeline-alter .item-timeline .t-text p {
  font-size: 13px;
  margin: 0;
  color: #3b3f5c;
  font-weight: 600;
}
.timeline-alter .item-timeline .t-text p a {
  color: #4361ee;
  font-weight: 600;
}

/*
=======================
    Timeline Simple
=======================
*/
.timeline-simple {
  margin-bottom: 45px;
  max-width: 1140px;
  margin-right: auto;
  margin-left: auto;
}
.timeline-simple h3 {
  font-size: 23px;
  font-weight: 600;
}
.timeline-simple p.timeline-title {
  position: relative;
  font-size: 19px;
  font-weight: 600;
  color: #4361ee;
  margin-bottom: 28px;
}
.timeline-simple .timeline-list p.meta-update-day {
  margin-bottom: 24px;
  font-size: 16px;
  font-weight: 600;
  color: #888ea8;
}
.timeline-simple .timeline-list .timeline-post-content {
  display: flex;
}
.timeline-simple .timeline-list .timeline-post-content > div > div {
  margin-top: 28px;
}
.timeline-simple .timeline-list .timeline-post-content:not(:last-child) > div > div {
  margin-bottom: 70px;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile {
  position: relative;
  z-index: 2;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  top: 15px;
  left: 34%;
  transform: translateX(-50%);
  width: 0;
  height: auto;
  top: 48px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
  z-index: -1;
  border-color: #ebedf2;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile img {
  width: 53px;
  height: 53px;
  border-radius: 50%;
  margin-right: 30px;
  box-shadow: 0px 4px 9px 0px rgba(31, 45, 61, 0.31);
}
.timeline-simple .timeline-list .timeline-post-content h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 0;
  color: #4361ee;
}
.timeline-simple .timeline-list .timeline-post-content svg {
  color: #888ea8;
  vertical-align: text-bottom;
  width: 21px;
  height: 21px;
}
.timeline-simple .timeline-list .timeline-post-content:hover svg {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.09);
}
.timeline-simple .timeline-list .timeline-post-content h6 {
  display: inline-block;
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 11px;
  color: #3b3f5c;
}
.timeline-simple .timeline-list .timeline-post-content:hover h6 {
  color: #888ea8;
}
.timeline-simple .timeline-list .timeline-post-content p.post-text {
  padding-left: 31px;
  color: #888ea8;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 28px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers {
  padding-left: 31px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers img {
  width: 38px;
  border-radius: 50%;
  margin-right: 7px;
  box-shadow: 1px 3px 7px 2px rgba(31, 45, 61, 0.31);
  transition: all 0.35s ease;
  cursor: pointer;
  margin-bottom: 5px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers img:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: none;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img {
  padding-left: 31px;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img {
  width: 20%;
  border-radius: 6px;
  box-shadow: 1px 3px 7px 2px rgba(31, 45, 61, 0.31);
  transition: all 0.35s ease;
  cursor: pointer;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: none;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img:not(:last-child) {
  margin-right: 23px;
}

@media (max-width: 767px) {
  .timeline-simple .timeline-list .timeline-post-content .post-gallery-img img {
    width: 150px;
    margin-bottom: 23px;
  }
}
@media (max-width: 575px) {
  .timeline-alter .item-timeline {
    display: block;
    text-align: center;
  }
  .timeline-alter .item-timeline .t-meta-time p, .timeline-alter .item-timeline .t-usr-txt p {
    margin: 0 auto;
  }
  .timeline-simple .timeline-list .timeline-post-content {
    display: block;
  }
  .timeline-simple .timeline-list .timeline-post-content div.user-profile {
    margin-bottom: 18px;
    text-align: center;
  }
  .timeline-simple .timeline-list .timeline-post-content div.user-profile:after {
    display: none;
  }
  .timeline-simple .timeline-list .timeline-post-content div.user-profile img {
    margin-right: 0;
  }
  .timeline-simple .timeline-list .timeline-post-content h4, .timeline-simple .timeline-list .timeline-post-content .meta-time-date {
    text-align: center;
  }
}
/*
=======================
    Timeline Simple
=======================
*/
.timeline {
  width: 85%;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  padding: 32px 0 32px 32px;
  border-left: 2px solid #eceffe;
  font-size: 15px;
}

.timeline-item {
  display: flex;
  gap: 24px;
}
.timeline-item + * {
  margin-top: 24px;
}
.timeline-item + .extra-space {
  margin-top: 48px;
}

.new-comment {
  width: 100%;
}

.timeline-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: -52px;
  flex-shrink: 0;
  overflow: hidden;
  box-shadow: 0 0 0 6px #e0e6ed;
}
.timeline-item-icon svg {
  width: 20px;
  height: 20px;
}
.timeline-item-icon.faded-icon {
  background-color: white;
  color: #0e1726;
}
.timeline-item-icon.filled-icon {
  background-color: #4361ee;
  color: #fff;
}

.timeline-item-description {
  display: flex;
  gap: 8px;
  color: #3b3f5c;
}
.timeline-item-description img {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  aspect-ratio: 1/1;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
}
.timeline-item-description a {
  color: #4361ee;
  font-weight: 600;
  text-decoration: none;
}
.timeline-item-description a:hover, .timeline-item-description a:focus {
  outline: 0;
  color: #888ea8;
}

.comment {
  margin-top: 12px;
  color: #3b3f5c;
  border-radius: 6px;
  padding: 16px;
  font-size: 1rem;
  border: 1px solid #e0e6ed;
  box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px;
}
.comment .btn-like {
  padding: 7px 13px;
  border: none;
  box-shadow: none;
  border-radius: 60px;
}
.comment .btn-like svg {
  width: 19px;
  height: 19px;
  vertical-align: sub;
}
.comment p {
  color: #515365;
}

.btn.square {
  background: transparent;
}
.btn.square svg {
  width: 24px;
  height: 24px;
  fill: #e2a03f;
  color: #0e1726;
}

.show-replies {
  color: #888ea8;
  background-color: transparent;
  border: 0;
  padding: 0;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 1rem;
  cursor: pointer;
}
.show-replies svg {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}
.show-replies:hover, .show-replies:focus {
  color: #3b3f5c;
}/*# sourceMappingURL=timeline.css.map */