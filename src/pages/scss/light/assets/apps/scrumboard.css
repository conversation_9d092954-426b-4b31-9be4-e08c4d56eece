/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/* Delete Modal*/
#deleteConformation .modal-content {
  border: 0;
  box-shadow: 2px 5px 17px 0 rgba(31, 45, 61, 0.1);
  padding: 30px;
}
#deleteConformation .modal-content .modal-header {
  border: none;
  padding: 0;
}
#deleteConformation .modal-content .modal-header .icon {
  padding: 7px 9px;
  background: rgba(231, 81, 90, 0.37);
  text-align: center;
  margin-right: 8px;
  border-radius: 50%;
}
#deleteConformation .modal-content .modal-header svg {
  width: 20px;
  color: #e7515a;
  fill: rgba(231, 81, 90, 0.37);
}
#deleteConformation .modal-content .modal-header .modal-title {
  color: #3b3f5c;
  font-size: 18px;
  font-weight: 700;
  align-self: center;
}
#deleteConformation .modal-content .modal-header .btn-close {
  color: #fff;
  background: none;
  opacity: 1;
  width: auto;
  height: auto;
  font-size: 20px;
}
#deleteConformation .modal-content .modal-body {
  padding: 28px 0;
}
#deleteConformation .modal-content .modal-body p {
  color: #888ea8;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 0;
}
#deleteConformation .modal-content .modal-footer {
  padding: 0;
  border: none;
}
#deleteConformation .modal-content .modal-footer [data-bs-dismiss=modal] {
  background-color: #fff;
  color: #e7515a;
  font-weight: 700;
  border: 1px solid #e8e8e8;
  padding: 10px 25px;
}
#deleteConformation .modal-content .modal-footer [data-remove=task] {
  color: #fff;
  font-weight: 600;
  padding: 10px 25px;
}

.task-list-section {
  display: flex;
  overflow-x: auto;
  flex-wrap: nowrap;
}

.task-list-container {
  min-width: 309px;
  padding: 0 15px;
  width: 320px;
}
.task-list-container:first-child {
  padding-left: 0;
}
.task-list-container:last-child {
  padding-right: 0;
}

/*  
    Connect Sorting Div
*/
.connect-sorting {
  padding: 15px;
  background: #ebedf2;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
}
.connect-sorting .task-container-header {
  display: flex;
  justify-content: space-between;
  padding: 18px 5px;
}
.connect-sorting .task-container-header .dropdown .dropdown-menu {
  padding: 11px;
}
.connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item {
  padding: 5px;
  font-size: 14px;
  font-weight: 700;
}
.connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item:hover {
  color: #009688;
}
.connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item.active, .connect-sorting .task-container-header .dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}
.connect-sorting .task-container-header h6 {
  font-size: 16px;
  font-weight: 700;
  color: #3b3f5c;
}
.connect-sorting .add-s-task {
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  text-align: center;
}
.connect-sorting .add-s-task:hover {
  transform: translateY(-3px);
}
.connect-sorting .add-s-task .addTask {
  display: block;
  color: #3b3f5c;
  font-size: 13px;
  font-weight: 700;
  text-align: center;
  display: inline-block;
  cursor: pointer;
}
.connect-sorting .add-s-task .addTask:hover {
  color: #4361ee;
}
.connect-sorting .add-s-task .addTask svg {
  width: 16px;
  height: 16px;
  vertical-align: text-top;
}

.scrumboard .task-header {
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20px 20px 0 20px;
}
.scrumboard .task-header h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0;
  color: #191e3a;
}
.scrumboard .task-header svg.feather-edit-2 {
  width: 18px;
  height: 18px;
  color: #888ea8;
  vertical-align: middle;
  fill: rgba(0, 23, 55, 0.08);
  cursor: pointer;
  padding: 0;
  margin-right: 5px;
}
.scrumboard .task-header svg.feather-edit-2:hover {
  color: #4361ee;
  fill: none;
}
.scrumboard .task-header svg.feather-trash-2 {
  color: #e7515a;
  margin-right: 6px;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  fill: rgba(231, 81, 90, 0.14);
  cursor: pointer;
}
.scrumboard .task-header svg.feather-trash-2:hover {
  fill: rgba(231, 81, 90, 0.37);
}
.scrumboard .card {
  background: #fff;
  border: none;
  border-radius: 4px;
  margin-bottom: 30px;
  border: none;
}
.scrumboard .card .card-body {
  padding: 0;
}
.scrumboard .card .card-body .task-body .task-bottom {
  display: flex;
  justify-content: space-between;
  padding: 12px 15px;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 span {
  font-size: 13px;
  font-weight: 600;
  width: 17px;
  height: 17px;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 span:hover {
  color: #4361ee;
  cursor: pointer;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 span:hover svg {
  color: #4361ee;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 svg {
  width: 18px;
  vertical-align: bottom;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 svg:not(:last-child) {
  margin-right: 5px;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg {
  width: 18px;
  cursor: pointer;
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  fill: rgba(0, 23, 55, 0.08);
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-edit-2 {
  width: 18px;
  height: 18px;
  color: #888ea8;
  vertical-align: middle;
  fill: none;
  cursor: pointer;
  padding: 0;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-edit-2:hover {
  color: #4361ee;
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-trash-2 {
  color: #e7515a;
  margin-right: 6px;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  fill: rgba(231, 81, 90, 0.14);
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg.feather-trash-2:hover {
  fill: rgba(231, 81, 90, 0.37);
}
.scrumboard .card .card-body .task-body .task-bottom div.tb-section-2 svg:not(:last-child) {
  margin-right: 5px;
}
.scrumboard .card.img-task .card-body .task-content {
  padding: 10px 10px 0 10px;
}
.scrumboard .card.img-task .card-body .task-content img {
  border-radius: 6px;
  height: 105px;
  width: 100%;
}
.scrumboard .card.simple-title-task .card-body .task-header {
  margin-bottom: 0;
  padding: 20px;
}
.scrumboard .card.simple-title-task .card-body .task-header div:nth-child(1) {
  width: 70%;
}
.scrumboard .card.simple-title-task .card-body .task-header div:nth-child(2) {
  width: 30%;
  text-align: right;
}
.scrumboard .card.simple-title-task .card-body .task-body .task-bottom {
  padding: 3px 15px 11px 15px;
}
.scrumboard .card.task-text-progress .card-body .task-content {
  margin-top: 20px;
}
.scrumboard .card.task-text-progress .card-body .task-content p {
  padding: 5px 20px 5px 20px;
  color: #3b3f5c;
}
.scrumboard .card.task-text-progress .card-body .task-content .progress {
  height: 9px;
  width: 100%;
  margin-right: 17px;
  margin-bottom: 0;
  align-self: center;
  background: #ebedf2;
}
.scrumboard .card.task-text-progress .card-body .task-content .progress .progress-bar {
  background-color: #009688 !important;
  border-color: #009688;
}
.scrumboard .card.task-text-progress .card-body .task-content > div {
  display: flex;
  padding: 5px 20px 5px 20px;
}
.scrumboard .card.task-text-progress .card-body .task-content > div p.progress-count {
  padding: 0;
  margin-bottom: 0;
}
.scrumboard .card.ui-sortable-helper {
  background-color: #4361ee;
  background: rgba(67, 97, 238, 0.28);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.scrumboard .card.ui-sortable-helper .task-header span {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper .task-header span svg {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper .task-header svg.feather-edit-2, .scrumboard .card.ui-sortable-helper .task-header svg.feather-trash-2 {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper .task-header h4 {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper.task-text-progress .card-body .task-content p {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper.task-text-progress .card-body .task-content .progress .progress-bar {
  background-color: #2196f3 !important;
}
.scrumboard .card.ui-sortable-helper .task-header svg.feather-user {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper .card-body .task-body .task-bottom div.tb-section-1 {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper .card-body .task-body .task-bottom div.tb-section-1 svg {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper .card-body .task-body .task-bottom div.tb-section-2 svg {
  color: #3b3f5c;
}
.scrumboard .card.ui-sortable-helper .card-body .task-content .progress {
  box-shadow: none;
}

/*
    img task
*/
/*
    task-text-progress
*/
/*
    Style On events
*/
/* On Drag Task */
.ui-state-highlight {
  position: relative;
  border-color: #009688;
  height: 141px;
  margin-bottom: 36px;
  border-radius: 15px;
  border: 1px dashed #009688;
  background-image: linear-gradient(45deg, rgba(27, 85, 226, 0.09) 25%, transparent 25%, transparent 50%, rgba(27, 85, 226, 0.09) 50%, rgba(27, 85, 226, 0.09) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
  animation: progress-bar-stripes 1s linear infinite;
}
.ui-state-highlight:before {
  content: "Drop";
  position: absolute;
  left: 41%;
  font-size: 19px;
  color: #009688;
  top: 50%;
  margin-top: -16px;
  font-weight: 600;
}

.connect-sorting-content {
  min-height: 60px;
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }
  100% {
    background-position: 0 0;
  }
}/*# sourceMappingURL=scrumboard.css.map */