@import '../../base/base';
/* Delete Modal*/

#deleteConformation .modal-content {
  border: 0;
  -webkit-box-shadow: 2px 5px 17px 0 rgba(31, 45, 61, 0.1);
  box-shadow: 2px 5px 17px 0 rgba(31, 45, 61, 0.1);
  padding: 30px;

  .modal-header {
    border: none;
    padding: 0;

    .icon {
      padding: 7px 9px;
      background: rgba(231, 81, 90, 0.37);
      text-align: center;
      margin-right: 8px;
      border-radius: 50%;
    }

    svg {
      width: 20px;
      color: $danger;
      fill: rgba(231, 81, 90, 0.37);
    }

    .modal-title {
      color: #3b3f5c;
      font-size: 18px;
      font-weight: 700;
      align-self: center;
    }

    .btn-close {
      color: #fff;
      background: none;
      opacity: 1;
      width: auto;
      height: auto;
      font-size: 20px;
    }
  }

  .modal-body {
    padding: 28px 0;

    p {
      color: #888ea8;
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 0;
    }
  }

  .modal-footer {
    padding: 0;
    border: none;

    [data-bs-dismiss="modal"] {
      background-color: #fff;
      color: $danger;
      font-weight: 700;
      border: 1px solid #e8e8e8;
      padding: 10px 25px;
    }

    [data-remove="task"] {
      color: #fff;
      font-weight: 600;
      padding: 10px 25px;
    }
  }
}

.task-list-section {
  display: flex;
  overflow-x: auto;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.task-list-container {
  min-width: 309px;
  padding: 0 15px;
  width: 320px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }
}

/*  
    Connect Sorting Div
*/

.connect-sorting {
  padding: 15px;
  background: #ebedf2;
  border-radius: 8px;
  border: 1px solid #e0e6ed;

  .task-container-header {
    display: flex;
    justify-content: space-between;
    padding: 18px 5px;

    .dropdown .dropdown-menu {
      padding: 11px;

      .dropdown-item {
        padding: 5px;
        font-size: 14px;
        font-weight: 700;

        &:hover {
          color: #009688;
        }

        &.active, &:active {
          background-color: transparent;
        }
      }
    }

    h6 {
      font-size: 16px;
      font-weight: 700;
      color: $dark;
    }
  }

  .add-s-task {
    transition: all 0.3s ease-out;
    -webkit-transition: all 0.3s ease-out;
    text-align: center;

    &:hover {
      -webkit-transform: translateY(-3px);
      transform: translateY(-3px);
    }

    .addTask {
      display: block;
      color: $dark;
      font-size: 13px;
      font-weight: 700;
      text-align: center;
      display: inline-block;
      cursor: pointer;

      &:hover {
        color: $primary;
      }

      svg {
        width: 16px;
        height: 16px;
        vertical-align: text-top;
      }
    }
  }
}

.scrumboard {
  .task-header {
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 0 20px;

    h4 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 0;
      color: #191e3a;
    }

    svg {
      &.feather-edit-2 {
        width: 18px;
        height: 18px;
        color: #888ea8;
        vertical-align: middle;
        fill: rgba(0, 23, 55, 0.08);
        cursor: pointer;
        padding: 0;
        margin-right: 5px;

        &:hover {
          color: $primary;
          fill: none;
        }
      }

      &.feather-trash-2 {
        color: $danger;
        margin-right: 6px;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        fill: rgba(231, 81, 90, 0.14);
        cursor: pointer;

        &:hover {
          fill: rgba(231, 81, 90, 0.37);
        }
      }
    }
  }

  .card {
    background: #fff;
    border: none;
    border-radius: 4px;
    margin-bottom: 30px;
    border: none;
    
    .card-body {
      padding: 0;

      .task-body .task-bottom {
        display: flex;
        justify-content: space-between;
        padding: 12px 15px;

        div {
          &.tb-section-1 {
            span {
              font-size: 13px;
              font-weight: 600;
              width: 17px;
              height: 17px;

              &:hover {
                color: $primary;
                cursor: pointer;

                svg {
                  color: $primary;
                }
              }
            }

            svg {
              width: 18px;
              vertical-align: bottom;

              &:not(:last-child) {
                margin-right: 5px;
              }
            }
          }

          &.tb-section-2 svg {
            width: 18px;
            cursor: pointer;
            color: #888ea8;
            margin-right: 6px;
            vertical-align: middle;
            width: 18px;
            height: 18px;
            fill: rgba(0, 23, 55, 0.08);

            &.feather-edit-2 {
              width: 18px;
              height: 18px;
              color: #888ea8;
              vertical-align: middle;
              fill: none;
              cursor: pointer;
              padding: 0;

              &:hover {
                color: $primary;
              }
            }

            &.feather-trash-2 {
              color: $danger;
              margin-right: 6px;
              vertical-align: middle;
              width: 18px;
              height: 18px;
              fill: rgba(231, 81, 90, 0.14);

              &:hover {
                fill: rgba(231, 81, 90, 0.37);
              }
            }

            &:not(:last-child) {
              margin-right: 5px;
            }
          }
        }
      }
    }

    &.img-task .card-body .task-content {
      padding: 10px 10px 0 10px;

      img {
        border-radius: 6px;
        height: 105px;
        width: 100%;
      }
    }

    &.simple-title-task .card-body {
      .task-header {
        margin-bottom: 0;
        padding: 20px;

        div {
          &:nth-child(1) {
            width: 70%;
          }

          &:nth-child(2) {
            width: 30%;
            text-align: right;
          }
        }
      }

      .task-body .task-bottom {
        padding: 3px 15px 11px 15px;
      }
    }

    &.task-text-progress .card-body .task-content {
      margin-top: 20px;

      p {
        padding: 5px 20px 5px 20px;
        color: $dark;
      }

      .progress {
        .progress-bar {
          background-color: #009688 !important;
          border-color: #009688;
        }

        height: 9px;
        width: 100%;
        margin-right: 17px;
        margin-bottom: 0;
        align-self: center;
        background: #ebedf2;
      }

      > div {
        display: flex;
        padding: 5px 20px 5px 20px;
      }

      > div p.progress-count {
        padding: 0;
        margin-bottom: 0;
      }
    }

    &.ui-sortable-helper {
      background-color: $primary;
      background: rgba(67, 97, 238, 0.28);
      backdrop-filter: blur(5px);

      .task-header {
        span {
          color: $dark;

          svg {
            color: $dark;
          }
        }

        svg {
          &.feather-edit-2, &.feather-trash-2 {
            color: $dark;
          }
        }

        h4 {
          color: $dark;
        }
      }

      &.task-text-progress .card-body .task-content {
        p {
          color: $dark;
        }

        .progress .progress-bar {
          background-color: #2196f3 !important;
        }
      }

      .task-header svg.feather-user {
        color: $dark;
      }

      .card-body {
        .task-body .task-bottom div {
          &.tb-section-1 {
            color: $dark;

            svg {
              color: $dark;
            }
          }

          &.tb-section-2 svg {
            color: $dark;
          }
        }

        .task-content .progress {
          box-shadow: none;
        }
      }
    }
  }
}

/*
    img task
*/

/*
    task-text-progress
*/

/*
    Style On events
*/

/* On Drag Task */

.ui-state-highlight {
  position: relative;
  border-color: #009688;
  height: 141px;
  margin-bottom: 36px;
  border-radius: 15px;
  border: 1px dashed #009688;
  background-image: linear-gradient(45deg, rgba(27, 85, 226, 0.09) 25%, transparent 25%, transparent 50%, rgba(27, 85, 226, 0.09) 50%, rgba(27, 85, 226, 0.09) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
  -webkit-animation: progress-bar-stripes 1s linear infinite;
  animation: progress-bar-stripes 1s linear infinite;

  &:before {
    content: 'Drop';
    position: absolute;
    left: 41%;
    font-size: 19px;
    color: #009688;
    top: 50%;
    margin-top: -16px;
    font-weight: 600;
  }
}

.connect-sorting-content {
  min-height: 60px;
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }

  100% {
    background-position: 0 0;
  }
}