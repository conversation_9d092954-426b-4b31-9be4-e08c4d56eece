/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.layout-px-spacing {
  min-height: calc(100vh - 142px) !important;
}

[class*=g-dot-] {
  position: relative;
}
[class*=g-dot-]:before {
  position: absolute;
  padding: 4px;
  content: "";
  background: transparent;
  border-radius: 50%;
  top: 15px;
  left: 0;
  border: 1px solid #515365;
}

.g-dot-primary:before {
  border-color: #2196f3;
  background: #2195f3;
}

.g-dot-warning:before {
  border-color: #e2a03f;
  background: #e2a03f;
}

.g-dot-success:before {
  border-color: #00ab55;
  background: #00ab55;
}

.g-dot-danger:before {
  border-color: #e7515a;
  background: #e7515a;
}

.mail-content-container.mailInbox [data-original-title=Restore], .mail-content-container.sentmail [data-original-title=Restore], .mail-content-container.important [data-original-title=Restore], .mail-content-container.spam [data-original-title=Restore] {
  display: none;
}
.mail-content-container.trashed [data-original-title=Reply], .mail-content-container.trashed [data-original-title=Forward], .mail-content-container.trashed [data-original-title=Print] {
  display: none;
}

.form-check-input {
  background-color: #bfc9d4;
  border-color: #bfc9d4;
}

.mail-box-container {
  position: relative;
  display: flex;
  border-radius: 8px;
  background-color: #fff;
  height: calc(100vh - 155px);
  border: 1px solid #e0e6ed;
}
.mail-box-container .avatar-sm {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 0.83333rem;
}
.mail-box-container .avatar {
  position: relative;
  display: inline-block;
  width: 34px;
  height: 34px;
  font-size: 12px;
}
.mail-box-container .avatar .avatar-title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #181d3a;
  color: #ebedf2;
}

.mail-overlay {
  display: none;
  position: absolute;
  width: 100vw;
  height: 100%;
  background: #3b3f5c !important;
  z-index: 4 !important;
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
.mail-overlay.mail-overlay-show {
  display: block;
  opacity: 0.7;
}

.tab-title {
  padding: 33px 15px;
  max-width: 115px;
  border-right: 1px solid #e0e6ed;
}
.tab-title .mail-btn-container {
  padding: 0 30px;
}
.tab-title #btn-compose-mail {
  transform: none;
  background: #805dca;
  border: none !important;
  padding: 7px 9px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1px;
  color: #fff !important;
  width: 40px;
  margin: 0 auto;
}
.tab-title #btn-compose-mail:hover {
  box-shadow: none;
}
.tab-title #btn-compose-mail svg {
  width: 22px;
  height: 22px;
}
.tab-title.mail-menu-show {
  left: 0;
  width: 100%;
  height: 100%;
}
.tab-title .nav-pills .nav-link.active, .tab-title .nav-pills .show > .nav-link {
  background-color: transparent;
  color: #4361ee;
  font-weight: 600;
  fill: none;
}
.tab-title .mail-categories-container {
  margin-top: 27px;
  padding: 0 0;
}
.tab-title .mail-sidebar-scroll {
  position: relative;
  margin: auto;
  width: 100%;
  overflow: auto;
  height: calc(100vh - 254px);
}
.tab-title .mail-sidebar-scroll .ps__rail-y {
  right: -15px !important;
}
.tab-title .nav-pills:nth-child(1) .nav-item:first-child a.nav-link {
  border-top: 1px solid #e0e6ed;
  padding-top: 24px;
}
.tab-title .nav-pills a.nav-link {
  position: relative;
  font-weight: 600;
  color: #3b3f5c;
  padding: 14px 0px 14px 0px;
  cursor: pointer;
  font-size: 14px;
  display: block;
  text-align: center;
  border-radius: 0;
  border-bottom: 1px solid #e0e6ed;
  transition: none;
}
.tab-title .nav-pills .nav-link.active svg, .tab-title .nav-pills .show > .nav-link svg {
  color: #4361ee;
  fill: none;
}
.tab-title .nav-pills a.nav-link svg {
  width: 19px;
  height: 19px;
  margin-bottom: 7px;
  fill: none;
  color: #888ea8;
}
.tab-title .nav-pills a.nav-link span.nav-names {
  display: block;
  letter-spacing: 1px;
  padding: 0;
}
.tab-title .nav-pills a.nav-link .mail-badge {
  background: #4361ee;
  border-radius: 50%;
  position: absolute;
  right: 8px;
  padding: 3px 0;
  height: 19px;
  width: 19px;
  color: #fff;
  font-weight: 700;
  font-size: 10px;
  top: 7px;
}

.group-section {
  font-weight: 700;
  font-size: 15px;
  display: inline-block;
  color: #0e1726;
  letter-spacing: 1px;
  margin-top: 22px;
  margin-bottom: 13px;
  display: flex;
  justify-content: center;
}
.group-section svg {
  color: #4361ee;
  margin-right: 6px;
  align-self: center;
  width: 17px;
  height: 17px;
}

.tab-title .nav-pills.group-list .nav-item a {
  position: relative;
  padding: 6px 45px 6px 41px;
  letter-spacing: 1px;
  border-radius: 5px;
  font-size: 12px;
  font-weight: 700;
  color: #888ea8;
  border-bottom: none !important;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-primary.active:before {
  background: #2196f3;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-warning.active:before {
  background: #e2a03f;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-success.active:before {
  background: #009688;
}
.tab-title .nav-pills.group-list .nav-item a.g-dot-danger.active:before {
  background: #e7515a;
}
.tab-title .nav-pills.group-list .nav-item a[class*=g-dot-]:before {
  position: absolute;
  padding: 4px;
  content: "";
  border-radius: 50%;
  top: 9px;
  left: 18px;
  transition: 0.6ms;
}
.tab-title .nav-pills .nav-item .dropdown-menu {
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  padding: 0;
  border: none;
}
.tab-title li.mail-labels a.dropdown-item {
  font-size: 13px;
  font-weight: 700;
  padding: 8px 18px;
}
.tab-title li.mail-labels a.dropdown-item:hover {
  background-color: #fff;
  color: #4361ee;
}
.tab-title li.mail-labels .label:after {
  position: absolute;
  content: "";
  height: 6px;
  width: 6px;
  border-radius: 50%;
  right: 15px;
  top: 43%;
}

/*Mail Labels*/
.actions-btn-tooltip.tooltip {
  opacity: 1;
  top: -11px !important;
}
.actions-btn-tooltip .arrow:before {
  border-top-color: #3b3f5c;
}
.actions-btn-tooltip .tooltip-inner {
  background: #3b3f5c;
  color: #fff;
  font-weight: 700;
  border-radius: 30px;
  padding: 4px 16px;
}

/*
=====================
    Mailbox Inbox
=====================
*/
.mailbox-inbox {
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
  max-width: 100%;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
}
.mailbox-inbox .mail-menu {
  margin: 12px 13px 12px 13px;
  width: 22px;
  border-radius: 0;
  color: #515365;
  align-self: center;
}
.mailbox-inbox .search {
  display: flex;
  border-bottom: 1px solid #e0e6ed;
  background: #0e1726;
  border-top-right-radius: 8px;
}
.mailbox-inbox .search input {
  border: none;
  padding: 12px 13px 12px 13px;
  background-color: #fff;
  border-radius: 0;
  border-top-right-radius: 8px;
  box-shadow: none;
  color: #e0e6ed;
}
.mailbox-inbox .action-center {
  display: flex;
  justify-content: space-between;
  background: transparent;
  padding: 14px 16px;
  border-bottom: 1px solid #e0e6ed;
}
.mailbox-inbox .action-center .new-control {
  font-weight: 600;
  color: #e0e6ed;
}
.mailbox-inbox .action-center .nav-link {
  padding: 0;
  display: inline-block;
}
.mailbox-inbox .action-center .more-actions .dropdown-menu.show {
  top: 30px !important;
}
.mailbox-inbox .action-center .dropdown-menu.d-icon-menu {
  padding: 0;
  border: 1px solid #e0e6ed;
  min-width: 6rem;
  border-radius: 8px;
  top: 11px !important;
  left: 9px !important;
  background: #fff;
}
.mailbox-inbox .action-center .dropdown-menu.d-icon-menu a {
  font-size: 14px;
  font-weight: 600;
  padding: 10px 23px 10px 43px;
  color: #888ea8;
  letter-spacing: 1px;
}
.mailbox-inbox .action-center .dropdown-menu.d-icon-menu a:hover {
  background-color: transparent;
  color: #4361ee;
}
.mailbox-inbox .action-center .dropdown-menu.d-icon-menu a[class*=g-dot-]:before {
  left: 19px;
}
.mailbox-inbox .action-center .dropdown-menu.d-icon-menu a.dropdown-item.active, .mailbox-inbox .action-center .dropdown-menu.d-icon-menu a.dropdown-item:active {
  background-color: transparent;
}
.mailbox-inbox .action-center .dropdown-menu.d-icon-menu a svg {
  vertical-align: middle;
  font-size: 15px;
  margin-right: 7px;
  color: #888ea8;
}
.mailbox-inbox .action-center .nav-link:after {
  display: none;
}
.mailbox-inbox .action-center svg {
  cursor: pointer;
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 20px;
  height: 20px;
}
.mailbox-inbox .action-center .nav-link.label-group svg {
  margin-right: 12px;
}
.mailbox-inbox .action-center svg:not(:last-child) {
  margin-right: 12px;
}
.mailbox-inbox .action-center svg.revive-mail, .mailbox-inbox .action-center svg.permanent-delete {
  display: none;
}
.mailbox-inbox .action-center.tab-trash-active .nav-link svg {
  display: none;
}
.mailbox-inbox .action-center.tab-trash-active svg.action-important, .mailbox-inbox .action-center.tab-trash-active svg.action-spam, .mailbox-inbox .action-center.tab-trash-active svg.action-delete {
  display: none;
}
.mailbox-inbox .action-center.tab-trash-active svg.revive-mail, .mailbox-inbox .action-center.tab-trash-active svg.permanent-delete {
  display: inline-block;
}
.mailbox-inbox .more-actions svg.feather-more-vertical {
  margin-right: 0;
}
.mailbox-inbox .message-box {
  padding: 0 0 0 0;
}
.mailbox-inbox .message-box .message-box-scroll {
  position: relative;
  margin: auto;
  width: 100%;
  overflow: auto;
  height: calc(100vh - 260px);
}
.mailbox-inbox .mail-item[id*=unread-] div.mail-item-heading .mail-item-inner .f-body .mail-title {
  font-weight: 700;
  color: #3b3f5c;
}
.mailbox-inbox .mail-item[id*=unread-] div.mail-item-heading .mail-item-inner .f-body .user-email {
  font-weight: 700;
  color: #4361ee;
}
.mailbox-inbox .mail-item[id*=unread-] div.mail-item-heading .mail-item-inner .mail-content-excerpt {
  font-weight: 600;
  color: #607d8b;
}
.mailbox-inbox .mail-item[id*=unread-] div.mail-item-heading .mail-item-inner .f-body .meta-time {
  font-weight: 700;
}
.mailbox-inbox .mail-item div.mail-item-heading {
  padding: 11px 10px 11px 0;
  cursor: pointer;
  position: relative;
  border-bottom: 1px solid #e0e6ed;
}
.mailbox-inbox .mail-item div.mail-item-heading:hover {
  background: #eceffe;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner {
  padding-left: 15px;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .n-chk {
  align-self: center;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-head {
  align-self: center;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-head img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body {
  align-self: center;
  display: flex;
  width: 100%;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body > div.meta-title-tag {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body > div.meta-mail-time {
  display: flex;
  justify-content: space-between;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .user-email {
  padding: 0 15px 0 20px;
  min-width: 215px;
  max-width: 215px;
  font-size: 15px;
  color: #607d8b;
  margin-bottom: 0;
  letter-spacing: 0px;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  align-self: center;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .meta-time {
  margin-bottom: 0;
  float: right;
  font-weight: 500;
  font-size: 12px;
  min-width: 75px;
  max-width: 80px;
  text-align: right;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .mail-title {
  font-size: 15px;
  color: #3b3f5c;
  margin-bottom: 2px;
  letter-spacing: 0px;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .tags {
  position: relative;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .tags span {
  display: none;
  margin-left: 11px;
}
.mailbox-inbox .mail-item div.mail-item-heading.personal .mail-item-inner .f-body .tags span.g-dot-primary, .mailbox-inbox .mail-item div.mail-item-heading.work .mail-item-inner .f-body .tags span.g-dot-warning, .mailbox-inbox .mail-item div.mail-item-heading.social .mail-item-inner .f-body .tags span.g-dot-success, .mailbox-inbox .mail-item div.mail-item-heading.private .mail-item-inner .f-body .tags span.g-dot-danger {
  display: inline-block;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .tags span[class*=g-dot-]:before {
  top: -11px;
  left: -13px;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .mail-content-excerpt {
  font-size: 14px;
  margin-bottom: 0;
  color: #607d8b;
  margin-left: 0;
  margin-right: 0;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  width: calc(100vw - 830px);
  align-self: center;
}
.mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .mail-content-excerpt svg.attachment-indicator {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  vertical-align: top;
}
.mailbox-inbox .mail-item.sentmail div.mail-item-heading .mail-item-inner .mail-content-excerpt, .mailbox-inbox .mail-item.draft div.mail-item-heading .mail-item-inner .mail-content-excerpt {
  margin-left: 31px;
}
.mailbox-inbox .mail-item div.mail-item-heading .attachments {
  margin: 0 auto;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  width: calc(100vw - 830px);
  display: none;
}
.mailbox-inbox .mail-item div.mail-item-heading .attachments span {
  display: inline-block;
  border: 1px solid #009688;
  padding: 1px 11px;
  border-radius: 30px;
  color: #0e1726;
  background: #009688;
  font-size: 12px;
  margin-right: 3px;
  font-weight: 700;
  margin-bottom: 2px;
  letter-spacing: 0px;
  max-width: 96px;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/*
=====================
    Content Box
=====================
*/
.content-box {
  background-color: #fff;
  position: absolute;
  top: 0;
  height: 100%;
  width: 0px;
  left: auto;
  right: -46px;
  overflow: hidden;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}
.content-box .msg-close {
  padding: 13px;
  background: #fff;
  border-bottom: 1px solid #e0e6ed;
}
.content-box svg.close-message {
  font-size: 15px;
  color: #3b3f5c;
  padding: 3px;
  align-self: center;
  cursor: pointer;
  margin-right: 12px;
}
.content-box .mail-title {
  font-size: 24px;
  font-weight: 600;
  color: #4361ee;
  margin-bottom: 0;
  align-self: center;
}

.mailbox-inbox .collapse {
  position: relative;
  height: calc(100vh - 213px);
}
.mailbox-inbox .mail-content-container {
  position: relative;
  height: auto;
  overflow: auto;
  padding: 25px;
  border-radius: 8px;
}
.mailbox-inbox .mail-content-container .user-info img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 8px;
  border: 3px solid #e0e6ed;
}
.mailbox-inbox .mail-content-container .user-info .avatar {
  margin-right: 8px;
}
.mailbox-inbox .mail-content-container .user-info .f-body {
  align-self: center;
}
.mailbox-inbox .mail-content-container .user-info .meta-title-tag .mail-usr-name {
  margin-bottom: 0;
  font-size: 18px;
  font-weight: 700;
  color: #2196f3;
}
.mailbox-inbox .mail-content-container .user-info .user-email {
  margin-bottom: 0;
  font-weight: 600;
  display: inline-block;
}
.mailbox-inbox .mail-content-container .user-info .user-email span {
  font-size: 16px;
  font-weight: 700;
}
.mailbox-inbox .mail-content-container .user-info .user-cc-mail {
  margin-bottom: 0;
  font-weight: 600;
  margin-left: 8px;
  display: inline-block;
}
.mailbox-inbox .mail-content-container .user-info .user-cc-mail span {
  font-size: 16px;
  font-weight: 700;
}
.mailbox-inbox .mail-content-container .user-info .meta-mail-time .meta-time {
  display: inline-block;
  font-weight: 700;
  margin-bottom: 0;
}
.mailbox-inbox .mail-content-container .mail-content-meta-date {
  font-size: 13px;
  font-weight: 600;
  color: #3b3f5c;
  display: inline-block;
  font-weight: 700;
  margin-bottom: 0;
}
.mailbox-inbox .mail-content-container .action-btns a {
  margin-right: 20px;
}
.mailbox-inbox .mail-content-container .action-btns svg {
  color: #eaeaec;
  font-weight: 600;
}
.mailbox-inbox .mail-content-container .action-btns svg.restore {
  position: relative;
}
.mailbox-inbox .mail-content-container .action-btns svg.restore:after {
  content: "";
  height: 28px;
  width: 2px;
  background: #eaeaec;
  position: absolute;
  border-radius: 50px;
  left: 9px;
  transform: rotate(30deg);
  top: -3px;
}
.mailbox-inbox .mail-content-container .mail-content-title {
  font-weight: 600;
  font-size: 20px;
  color: #515365;
  margin-bottom: 25px;
}
.mailbox-inbox .mail-content-container p {
  font-size: 14px;
  color: #888ea8;
}
.mailbox-inbox .mail-content-container p.mail-content {
  padding-top: 45px;
  border-top: 1px solid #e0e6ed;
  margin-top: 20px;
}
.mailbox-inbox .mail-content-container .attachments {
  margin-top: 55px;
  margin-bottom: 0;
}
.mailbox-inbox .mail-content-container .attachments .attachments-section-title {
  font-weight: 600;
  color: #3b3f5c;
  font-size: 16px;
  border-bottom: 1px solid #e0e6ed;
  padding-bottom: 9px;
  margin-bottom: 20px;
}
.mailbox-inbox .mail-content-container .attachment {
  display: inline-block;
  padding: 9px;
  border-radius: 5px;
  margin-bottom: 10px;
  cursor: pointer;
  min-width: 150px;
  max-width: 235px;
}
.mailbox-inbox .mail-content-container .attachment svg {
  font-size: 18px;
  margin-right: 13px;
  color: #4361ee;
  align-self: center;
}
.mailbox-inbox .mail-content-container .attachment .file-name {
  color: #3b3f5c;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 0;
  word-break: break-word;
}
.mailbox-inbox .mail-content-container .attachment .file-size {
  color: #888ea8;
  font-size: 11px;
  text-align: left;
  font-weight: 600;
  margin-bottom: 0;
}

#editor-container {
  height: 200px;
  border: 1px solid #bfc9d4;
}

.ql-toolbar.ql-snow {
  border: 1px solid #bfc9d4;
  margin-top: 30px;
}

.ql-container.ql-snow {
  border: 1px solid #191e3a;
}

.modal-content .modal-body .compose-box p {
  color: #3b3f5c;
}
.modal-content .modal-body .compose-box p svg {
  width: 20px;
  height: 20px;
  vertical-align: text-bottom;
  color: #009688;
}

input[type=file]::file-selector-button, input[type=file]::-webkit-file-upload-button {
  background-color: #1b2e4b !important;
  color: #fff;
}

.ql-editor.ql-blank::before {
  color: #bfc9d4;
}

@keyframes fadeInUp {
  from {
    transform: translate3d(0, 40px, 0);
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: both;
}

.animatedFadeInUp {
  opacity: 0;
}

.fadeInUp {
  opacity: 0;
  animation-name: fadeInUp;
  -webkit-animation-name: fadeInUp;
}

@media (min-width: 1200px) {
  .main-container:not(.sidebar-closed) .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .mail-content-excerpt {
    width: calc(100vw - 980px) !important;
  }
}
@media (min-width: 992px) {
  .main-container:not(.sidebar-closed) .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .mail-content-excerpt {
    width: calc(100vw - 808px);
  }
  .main-container:not(.sidebar-closed) .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .user-email {
    min-width: 170px;
    max-width: 170px;
  }
  .main-container:not(.sidebar-closed) .mailbox-inbox .mail-item div.mail-item-heading .attachments {
    width: calc(100vw - 940px);
  }
}
@media (max-width: 991px) {
  .mail-box-container {
    overflow-x: hidden;
    overflow-y: auto;
  }
  .mailbox-inbox .search input {
    border-left: 1px solid #e0e6ed;
  }
  .tab-title {
    position: absolute;
    z-index: 4;
    left: -147px;
    width: 0;
    background: #fff;
  }
  .tab-title.mail-menu-show {
    left: 0;
    width: 100%;
    min-width: 111px;
  }
  .mailbox-inbox {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .mailbox-inbox .mail-menu {
    margin: 12px 13px 8px 13px;
  }
  .mailbox-inbox .search {
    background-color: #fff;
    padding: 0;
  }
  .mailbox-inbox .action-center {
    padding: 14px 14px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading:hover {
    background: transparent;
    border: none !important;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner {
    padding-left: 14px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .mail-content-excerpt {
    width: calc(100vw - 527px);
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .user-email {
    min-width: 170px;
    max-width: 170px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .attachments {
    width: calc(100vw - 527px);
    padding: 0 15px;
  }
}
@media (max-width: 767px) {
  .new-control.new-checkbox .new-control-indicator {
    margin-right: 10px;
  }
  .mailbox-inbox {
    display: block;
  }
  .mailbox-inbox .mail-item div.mail-item-heading {
    margin: 0;
    padding: 20px 10px 20px 0;
    border: none;
    border-radius: 0;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-head img {
    width: 35px;
    height: 35px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body {
    display: block;
  }
  .mailbox-inbox .message-box {
    width: 100%;
    margin-bottom: 40px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body > div.meta-title-tag {
    padding-left: 10px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .user-email {
    padding: 0 0 0 10px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .meta-time {
    min-width: auto;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .mail-content-excerpt {
    width: calc(100vw - 192px);
    padding-right: 7px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .tags {
    position: absolute;
    right: 5px;
    top: 23px;
    width: 60px;
  }
  .mailbox-inbox .mail-item.sentmail div.mail-item-heading .mail-item-inner .mail-content-excerpt, .mailbox-inbox .mail-item.draft div.mail-item-heading .mail-item-inner .mail-content-excerpt {
    margin-left: 0;
    width: calc(100vw - 178px);
  }
  .mailbox-inbox .mail-item div.mail-item-heading .attachments {
    width: calc(100vw - 192px);
    padding: 0 11px;
  }
  .mailbox-inbox .mail-item.sentmail div.mail-item-heading .attachments {
    margin: 0 0 0 40px;
  }
}
@media (max-width: 575px) {
  .mailbox-inbox .message-box {
    margin-bottom: 0;
  }
  .mailbox-inbox .mail-content-container .user-info {
    display: block !important;
  }
  .mailbox-inbox .mail-content-container .user-info img {
    margin-bottom: 10px;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body > div {
    display: block;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body > div.meta-mail-time {
    display: block;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .f-body .meta-time {
    margin-bottom: 0;
    float: none;
  }
  .mailbox-inbox .mail-item div.mail-item-heading .mail-item-inner .mail-content-excerpt {
    margin-left: 0;
    margin-right: 0;
    width: calc(100vw - 215px);
  }
  .mailbox-inbox .mail-content-container .action-btns a {
    margin-right: 0;
  }
  .compose-box .compose-content form .mail-form select {
    margin-left: 3px;
    margin-top: 10px;
  }
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /* IE10+ CSS styles go here */
  .tab-title {
    width: 100%;
  }
  .mailbox-inbox .mail-content-container .attachment .media .media-body {
    flex: none;
  }
}/*# sourceMappingURL=mailbox.css.map */