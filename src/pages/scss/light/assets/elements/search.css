/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*
  Live Search
*/
.filtered-list-search {
  margin-top: 0;
  margin-bottom: 50px;
}
.filtered-list-search form > div {
  position: relative;
}
.filtered-list-search form input {
  border: 1px solid #e0e6ed;
}
.filtered-list-search form input:focus {
  box-shadow: 0 0 4px 2px rgba(31, 45, 61, 0.1);
}
.filtered-list-search form button {
  border-radius: 50%;
  padding: 6px 7px;
  position: absolute;
  right: 5px;
  top: 5px;
}
.filtered-list-search form input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #bfc9d4;
}
.filtered-list-search form input::-moz-placeholder {
  /* Firefox 19+ */
  color: #bfc9d4;
}
.filtered-list-search form input:-ms-input-placeholder {
  /* IE 10+ */
  color: #bfc9d4;
}
.filtered-list-search form input:-moz-placeholder {
  /* Firefox 18- */
  color: #bfc9d4;
}

.searchable-container {
  max-width: 1140px;
  margin: 0 auto;
}

.searchable-items {
  padding: 13px;
  border: 1px solid #e0e6ed;
  border-radius: 10px;
}

.searchable-container .searchable-items {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.searchable-container .items {
  flex-direction: row;
  align-items: center;
  padding: 0.75rem 0.625rem;
  position: relative;
  display: flex;
  min-width: 0;
  word-wrap: break-word;
  justify-content: space-between;
  background: #fff;
  margin-bottom: 15px;
  border-radius: 14px;
  padding: 13px 18px;
  width: 100%;
  color: #0e1726;
  min-width: 625px;
  cursor: pointer;
  box-shadow: 0px 2px 9px 2px rgba(31, 45, 61, 0.1);
  transition: transform 0.3s;
}
.searchable-container .items:hover {
  transform: translateY(0) scale(1.03);
  transform: translateY(0) scale(1.01);
}
.searchable-container .items .user-profile {
  display: flex;
}
.searchable-container .items .user-profile img {
  width: 43px;
  height: 43px;
  border-radius: 5px;
}
.searchable-container .items .user-name p, .searchable-container .items .user-work p, .searchable-container .items .user-email p {
  margin-bottom: 0;
  color: #888ea8;
  font-weight: 600;
}
.searchable-container .items .action-btn p {
  margin-bottom: 0;
  color: #506690;
  cursor: pointer;
  font-weight: 600;
}
.searchable-container .items:hover .serial-number p, .searchable-container .items:hover .user-name p, .searchable-container .items:hover .user-work p, .searchable-container .items:hover .user-email p, .searchable-container .items:hover .action-btn p {
  color: #00ab55;
}

/*
    Search Box
*/
.search-input-group-style.input-group .input-group-prepend .input-group-text svg {
  color: #4361ee;
}
.search-input-group-style input {
  border: 1px solid #e0e6ed;
  border-radius: 4px;
}/*# sourceMappingURL=search.css.map */