/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*
    ============================
        Pagination container
    =============================
*/
.paginating-container {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}
.paginating-container .prev svg, .paginating-container .next svg {
  width: 18px;
  height: 18px;
  vertical-align: text-bottom;
}
.paginating-container .pagination {
  margin-bottom: 0;
}
.paginating-container li {
  padding: 10px 0;
  font-weight: 600;
  color: #3b3f5c;
  border-radius: 4px;
}
.paginating-container li a {
  padding: 10px 15px;
  font-weight: 600;
  color: #3b3f5c;
}
.paginating-container li:not(:last-child) {
  margin-right: 4px;
}

/*
    Default Style
*/
.pagination-default li {
  border: 2px solid #e0e6ed;
}
.pagination-default li:hover {
  border: 2px solid #4361ee !important;
}
.pagination-default li:hover a {
  color: #4361ee;
}
.pagination-default li.active {
  border: 2px solid #4361ee !important;
  color: #4361ee;
}
.pagination-default li a.active:hover, .pagination-default li.active a {
  color: #4361ee;
}
.pagination-default .prev {
  border: 2px solid #e0e6ed;
}
.pagination-default .prev:hover {
  border: 2px solid #4361ee;
}
.pagination-default .prev:hover a, .pagination-default .prev:hover svg {
  color: #4361ee;
}
.pagination-default .next {
  border: 2px solid #e0e6ed;
}
.pagination-default .next:hover {
  border: 2px solid #4361ee;
}
.pagination-default .next:hover a, .pagination-default .next:hover svg {
  color: #4361ee;
}

/* 
    Solid Style
*/
.pagination-solid li {
  background-color: #e0e6ed;
}
.pagination-solid li:hover a {
  color: #4361ee;
}
.pagination-solid li.active {
  background-color: #4361ee !important;
  color: #fff;
}
.pagination-solid li a.active:hover, .pagination-solid li.active a {
  color: #fff;
}
.pagination-solid .prev {
  background-color: #e0e6ed;
}
.pagination-solid .prev:hover {
  background-color: #4361ee;
}
.pagination-solid .prev:hover a, .pagination-solid .prev:hover svg {
  color: #fff;
}
.pagination-solid .next {
  background-color: #e0e6ed;
}
.pagination-solid .next:hover {
  background-color: #4361ee;
}
.pagination-solid .next:hover a, .pagination-solid .next:hover svg {
  color: #fff;
}

/*    
    ===================
        No Spacing
    ===================
*/
.pagination-no_spacing {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}
.pagination-no_spacing .prev {
  background-color: #e0e6ed;
  border-radius: 50%;
  padding: 10px 11px;
  margin-right: 5px;
}
.pagination-no_spacing .prev:hover {
  background-color: #4361ee;
}
.pagination-no_spacing .prev:hover svg {
  color: #fff;
}
.pagination-no_spacing .next {
  background-color: #e0e6ed;
  border-radius: 50%;
  padding: 10px 11px;
  margin-left: 5px;
}
.pagination-no_spacing .next:hover {
  background-color: #4361ee;
}
.pagination-no_spacing .next:hover svg {
  color: #fff;
}
.pagination-no_spacing .prev svg, .pagination-no_spacing .next svg {
  width: 18px;
  height: 18px;
  vertical-align: text-bottom;
}
.pagination-no_spacing .pagination {
  margin-bottom: 0;
}
.pagination-no_spacing li {
  background-color: #e0e6ed;
  padding: 10px 0;
  font-weight: 600;
  color: #3b3f5c;
}
.pagination-no_spacing li:first-child {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}
.pagination-no_spacing li:last-child {
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}
.pagination-no_spacing li a {
  padding: 10px 15px;
  font-weight: 600;
  color: #3b3f5c;
}
.pagination-no_spacing li a.active {
  background-color: #4361ee !important;
  border-radius: 6px;
  color: #fff;
}
.pagination-no_spacing li a.active:hover {
  color: #fff;
}
.pagination-no_spacing li a:hover {
  color: #4361ee;
}

/*
    =======================
        Custom Pagination
    =======================
*/
/*
    Custom Solid
*/
.pagination-custom_solid {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}
.pagination-custom_solid .prev {
  background-color: #e0e6ed;
  border-radius: 50%;
  padding: 10px 11px;
  margin-right: 25px;
}
.pagination-custom_solid .prev:hover {
  background-color: #4361ee;
}
.pagination-custom_solid .prev:hover svg {
  color: #fff;
}
.pagination-custom_solid .next {
  background-color: #e0e6ed;
  border-radius: 50%;
  padding: 10px 11px;
  margin-left: 25px;
}
.pagination-custom_solid .next:hover {
  background-color: #4361ee;
}
.pagination-custom_solid .next:hover svg {
  color: #fff;
}
.pagination-custom_solid .prev svg, .pagination-custom_solid .next svg {
  width: 18px;
  height: 18px;
  vertical-align: text-bottom;
}
.pagination-custom_solid .pagination {
  margin-bottom: 0;
}
.pagination-custom_solid li {
  background-color: #e0e6ed;
  padding: 10px 0;
  font-weight: 600;
  color: #3b3f5c;
}
.pagination-custom_solid li:first-child {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}
.pagination-custom_solid li:last-child {
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}
.pagination-custom_solid li a {
  padding: 10px 15px;
  font-weight: 600;
  color: #3b3f5c;
}
.pagination-custom_solid li a.active {
  background-color: #4361ee !important;
  border-radius: 6px;
  color: #fff;
}
.pagination-custom_solid li a.active:hover {
  color: #fff;
}
.pagination-custom_solid li a:hover {
  color: #4361ee;
}

/*
    Custom Outline
*/
.pagination-custom_outline {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}
.pagination-custom_outline .prev {
  border: 2px solid #e0e6ed;
  border-radius: 50%;
  padding: 8px 11px;
  margin-right: 25px;
}
.pagination-custom_outline .prev:hover {
  border: 2px solid #4361ee;
}
.pagination-custom_outline .prev:hover svg {
  color: #4361ee;
}
.pagination-custom_outline .next {
  border: 2px solid #e0e6ed;
  border-radius: 50%;
  padding: 8px 11px;
  margin-left: 25px;
}
.pagination-custom_outline .next:hover {
  border: 2px solid #4361ee;
}
.pagination-custom_outline .next:hover svg {
  color: #4361ee;
}
.pagination-custom_outline .prev svg, .pagination-custom_outline .next svg {
  width: 16px;
  height: 16px;
  vertical-align: text-bottom;
}
.pagination-custom_outline .pagination {
  margin-bottom: 0;
}
.pagination-custom_outline li {
  padding: 10px 0;
  font-weight: 600;
  color: #888ea8;
  border: 1px solid #e0e6ed;
}
.pagination-custom_outline li.active {
  background-color: transparent;
}
.pagination-custom_outline li:first-child {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}
.pagination-custom_outline li:last-child {
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}
.pagination-custom_outline li a {
  padding: 10px 15px;
  font-weight: 600;
  color: #3b3f5c;
}
.pagination-custom_outline li a:hover {
  color: #4361ee;
}
.pagination-custom_outline li.active a {
  background-color: transparent;
  border: 2px solid #4361ee !important;
  border-radius: 6px;
  color: #4361ee;
}/*# sourceMappingURL=custom-pagination.css.map */