/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*      Alert       */
.alert {
  border-radius: 5px;
  margin-bottom: 15px;
  padding: 0.9375rem;
}
.alert .btn {
  margin-right: 27px;
}
.alert .btn:hover {
  box-shadow: none;
}
.alert .alert-icon svg {
  vertical-align: middle;
  width: 33px;
  height: 33px;
  stroke-width: 1.2;
}
.alert .btn-close {
  color: #000;
  opacity: 1;
  width: 18px;
  background: transparent;
  padding: 13px 12px;
  box-shadow: none;
}
.alert .btn-close svg {
  width: 18px;
  height: 18px;
}

/*Default Alerts*/
.alert-primary {
  color: #fff;
  background-color: #4361ee;
  border-color: #4361ee;
}

.alert-warning {
  color: #fff;
  background-color: #e2a03f;
  border-color: #e2a03f;
}

.alert-success {
  color: #fff;
  background-color: #00ab55;
  border-color: #00ab55;
}

.alert-info {
  color: #fff;
  background-color: #2196f3;
  border-color: #2196f3;
}

.alert-danger {
  color: #fff;
  background-color: #e7515a;
  border-color: #e7515a;
}

.alert-dark {
  color: #fff;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

/*Outline Alerts*/
.alert-outline-primary {
  border-color: #4361ee;
  border-radius: 5px;
}

.alert-outline-warning {
  border-color: #dea82a;
  border-radius: 5px;
}

.alert-outline-success {
  border-color: #00ab55;
  border-radius: 5px;
}

.alert-outline-info {
  border-color: #009eda;
  border-radius: 5px;
}

.alert-outline-danger {
  border-color: #e7515a;
  border-radius: 5px;
}

.alert-outline-dark {
  border-color: #454656;
  border-radius: 5px;
}

.alert.alert-light .close {
  color: #0e1726;
}
.alert.solid-alert-3 .close, .alert.solid-alert-4 .close {
  color: #000;
}

.hide-default {
  display: none;
}

/*      Light Alert         */
.btn-light {
  border-color: transparent;
}

.alert-light-primary {
  color: #4361ee;
  background-color: #eceffe;
  border-color: rgba(67, 97, 238, 0.55);
}
.alert-light-primary svg.close {
  color: #4361ee;
}

.alert-light-warning {
  color: #e2a03f;
  background-color: #fcf5e9;
  border-color: rgba(226, 160, 63, 0.55);
}
.alert-light-warning svg.close {
  color: #e2a03f;
}

.alert-light-success {
  color: #00ab55;
  background-color: #e6f6ee;
  border-color: rgba(26, 188, 156, 0.55);
}
.alert-light-success svg.close {
  color: #00ab55;
}

.alert-light-info {
  color: #2196f3;
  background-color: #e6f4ff;
  border-color: rgba(33, 150, 243, 0.55);
}
.alert-light-info svg.close {
  color: #2196f3;
}

.alert-light-danger {
  color: #e7515a;
  background-color: #fbeced;
  border-color: rgba(231, 81, 90, 0.55);
}
.alert-light-danger svg.close {
  color: #e7515a;
}

.alert-light-dark {
  color: #515365;
  background-color: #eaeaec;
  border-color: rgba(59, 63, 92, 0.55);
}
.alert-light-dark svg.close {
  color: #3b3f5c;
}
.alert-light-dark svg:not(.close) {
  color: #fff !important;
}

/*  Background Alerts      */
.alert-background {
  color: #fff;
  background: #fff url(../../../img/ab-1.jpeg) no-repeat center center;
  background-size: cover;
  border: none;
}

/*  Gradient Alerts      */
.alert-gradient {
  color: #fff;
  border: none;
  background-size: cover;
  background-image: linear-gradient(135deg, #bc1a4e 0%, #004fe6 100%);
}

/* Custom Alerts */
/* Default */
.custom-alert-1 {
  background-color: #7d30cb;
  border-radius: 5px;
  color: #fff;
}
.custom-alert-1 .btn-close {
  top: 9px;
}
.custom-alert-1 .alert-icon {
  margin-right: 25px;
}
.custom-alert-1 .media-body {
  display: flex;
  justify-content: space-between;
}
.custom-alert-1 .alert-text {
  margin-right: 10px;
}
.custom-alert-1 .alert-text strong, .custom-alert-1 .alert-text span {
  vertical-align: sub;
}

/*  Alert with Icon */
.alert-icon-left {
  border-left: 64px solid;
}
.alert-icon-left svg:not(.close) {
  color: #FFF;
  width: 4rem;
  left: -4rem;
  text-align: center;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.alert-icon-right {
  border-right: 64px solid;
}
.alert-icon-right svg:not(.close) {
  color: #FFF;
  width: 4rem;
  right: -4rem;
  text-align: center;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.alert-icon-right i {
  float: left;
  margin-right: 7px;
}

.alert[class*=alert-arrow-]:before {
  content: "";
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  border-left: 8px solid;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left-color: inherit;
  margin-top: -8px;
}
.alert.alert-arrow-right:before {
  left: auto;
  right: 0;
  border-left: 0;
  border-right: 8px solid;
  border-right-color: inherit;
}

@media (max-width: 575px) {
  .custom-alert-1 .media-body {
    display: block;
  }
  .alert .btn {
    margin-top: 8px;
  }
}/*# sourceMappingURL=alert.css.map */