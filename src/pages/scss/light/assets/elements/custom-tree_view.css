/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*  Tree View   */
.treeview {
  list-style: none;
  padding: 0;
  margin-bottom: 0;
  position: relative;
}
.treeview li p {
  font-size: 13px;
  color: #888ea8;
  font-weight: 500;
}
.treeview .tv-item .tv-header {
  padding: 6px 0;
  cursor: pointer;
  position: relative;
}
.treeview .tv-item .tv-header .tv-collapsible {
  display: flex;
}
.treeview .tv-item .tv-header .tv-collapsible .icon {
  margin-right: 6px;
  align-self: center;
}
.treeview .tv-item .tv-header .tv-collapsible .icon svg {
  width: 14px;
  height: 14px;
  transition: 0.5s;
}
.treeview .tv-item .tv-header .tv-collapsible .title {
  margin-bottom: 0;
  align-self: center;
  font-size: 14px;
  color: #3b3f5c;
  font-weight: 600;
}
.treeview .tv-item .tv-header .tv-collapsible:not(.collapsed) .icon svg.icon-tabler-chevron-right {
  transform: rotate(90deg);
  color: #3b3f5c;
}
.treeview .tv-item .treeview-collapse .treeview {
  position: relative;
}
.treeview .tv-item .treeview-collapse .treeview:before {
  content: "";
  position: absolute;
  height: calc(100% - 10px);
  width: 1px;
  /* background: #000; */
  top: 0;
  left: -24px;
  display: block;
  border-right: 1px dashed #888ea8;
  display: none;
}
.treeview .tv-item .treeview-collapse.show .treeview:before {
  display: block;
}
.treeview .treeview {
  margin-left: 30px;
  list-style: none;
  padding: 0;
}
.treeview .treeview .tv-item {
  position: relative;
}
.treeview .treeview .tv-item:before {
  content: "";
  position: absolute;
  height: 1px;
  width: 18px;
  /* background: #000; */
  left: -22px;
  top: 10px;
  border-bottom: 1px dashed #888ea8;
}
.treeview .treeview .tv-item.tv-folder:before {
  left: -22px;
  top: 18px;
}
.treeview .treeview .tv-item:not(.tv-folder) {
  padding-left: 5px;
}
.treeview.folder-structure .tv-item .tv-header {
  padding: 6px 0;
  cursor: pointer;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible {
  display: flex;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible .icon {
  margin-right: 6px;
  align-self: center;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible .icon svg {
  width: 20px;
  height: 20px;
  transition: 0.5s;
  color: #e2a03f;
  fill: #e2a03f;
}
.treeview.folder-structure .tv-item .tv-header .tv-collapsible:not(.collapsed) .icon svg {
  color: #e2a03f;
  fill: rgba(226, 160, 63, 0.4);
}
.treeview.folder-structure.treeview .tv-item:not(.tv-folder) span.icon {
  margin-left: 5px;
}
.treeview.folder-structure.treeview .tv-item:not(.tv-folder) span.icon svg {
  color: #888ea8;
  fill: rgba(67, 97, 238, 0.2);
  width: 20px;
  height: 20px;
}
.treeview.folder-structure.treeview .tv-item:not(.tv-folder) p {
  display: inline-block;
}
.treeview.folder-structure.treeview .tv-item:before {
  top: 14px;
}
.treeview.folder-structure .tv-item .treeview-collapse .treeview:before {
  height: calc(100% - 16px);
}/*# sourceMappingURL=custom-tree_view.css.map */