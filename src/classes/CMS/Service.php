<?php
namespace PhpBook\CMS;                                   // Namespace declaration

class Service
{
  protected $db;                                       // Holds ref to Database object

  public function __construct(Database $db)
  {
    $this->db = $db;                                 // Add ref to Database object
  }

  // Get individual article
  public function get(string $service_name, int $id)
  {
    $table_name = $service_name . $id;
    $sql = "SELECT * FROM $table_name;";

    return $this->db->runSQL($sql)->fetch();

    //return ($this->db->runSQL($sql)->fetch());



  }

  // Get summaries of articles
  public function getAll(string $service_name, int $id)
  {
    $table_name = $service_name . $id;                             // Max articles to return
    $sql = "SELECT * FROM $table_name;";
    return $this->db->runSQL($sql)->fetchAll(); // Return data
  }

  public function getRepairAll()
  {
    $sql = "SELECT * FROM repair;";
    return $this->db->runSQL($sql)->fetchAll(); // Return data
  }

  public function getPrinterAll()
  {
    $sql = "SELECT * FROM printer;";
    return $this->db->runSQL($sql)->fetchAll(); // Return data
  }

  public function getCrepairAll()
  {
    $sql = "SELECT * FROM crepair;";
    return $this->db->runSQL($sql)->fetchAll(); // Return data
  }

  public function getToiletAll()
  {
    $sql = "SELECT * FROM toilet;";
    return $this->db->runSQL($sql)->fetchAll(); // Return data
  }

  public function getSciAll()
  {
    $sql = "SELECT * FROM sci;";
    return $this->db->runSQL($sql)->fetchAll(); // Return data
  }

  public function getRowCount($service_name, $SD_SCHUL_CODE)
  {
    
    $table_name = $service_name . $SD_SCHUL_CODE;                             // Max articles to return
    $sql = "SELECT COUNT(id) FROM $table_name;";
    return $this->db->runSQL($sql)->fetch(); // Return data
  }


  

}