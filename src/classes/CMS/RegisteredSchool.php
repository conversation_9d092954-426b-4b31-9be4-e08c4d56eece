<?php
namespace PhpBook\CMS;                                   // Namespace declaration

class repair
{
    protected $db;                                       // Holds ref to Database object

    public function __construct(Database $db)
    {
        $this->db = $db;                                 // Add ref to Database object
    }

    // Get individual article
    public function get(string $service_name, int $id)
    {
        $table_name = $service_name . $id;
        $sql = "SELECT * FROM $table_name;";

    return $this->db->runSQL($sql)->fetch();    

    }

  // public function getExist(string $service_name, string $id): bool
  // {
  //   try {

  //     $this->db->beginTransaction();
  //     $sql = "SELECT * FROM repair WHERE service_name = :service_name AND SD_SCHUL_CODE = :id;";
  
  //     $result = $this->db->runSql($sql);
  //     var_dump($result);
  //     $this->db->commit();
  //     return true;
  //   } catch (\PDOException $e) {         // If an integrity constraint
  //       return false;                            // Return false
      
  //   }
  // }

    // Get summaries of articles
    public function getAll(string $service_name, int $id)
    {
      $table_name = $service_name . $id;                             // Max articles to return
      $sql = "SELECT * FROM $table_name;";
      return $this->db->runSQL($sql)->fetchAll(); // Return data
    }

    public function getWantsAll(string $service_name, int $id, string $state)
    {
  
      $table_name = $service_name . $id;
      $arguments['state'] = $state;                          // Max articles to return
      $sql = "SELECT id,grade,class,request,state FROM $table_name WHERE state = :state ORDER BY id DESC;";
      return $this->db->runSQL($sql, $arguments)->fetchAll(); // Return data
    }

    public function getCompleteLimit(string $service_name, int $id)
    {
      $table_name = $service_name . $id;                             // Max articles to return
      $sql = "SELECT * FROM $table_name WHERE state = '완료';";
      return $this->db->runSQL($sql)->fetchAll(); // Return data
    }

    public function setWant(string $service_name, int $id, string $grade, string $class, string $request, string $state)
    {
      $table_name = $service_name . $id;                           // Max articles to return
      $sql = "INSERT INTO $table_name (grade,class,request,state) VALUES (:grade,:class,:request,:state);";
   
      return $this->db->runSQL($sql, [$grade, $class, $request, $state])->fetch(); // Return data
    }

    public function updateState(string $service_name, int $id, string $want, int $selected_id) : bool
    { 
      try {                                            // Try to update data
      $table_name = $service_name . $id;
      echo $table_name;
     
      $arguments['want']        = $want;                       // Max articles to return'
      $arguments['selected_id'] = $selected_id;                       // Max articles to return'
      $sql = "UPDATE $table_name SET state = :want 
                 WHERE id = :selected_id;"; 
                    // SQL statement
        $this->db->runSQL($sql, $arguments);
      return true;                                 // Update worked
        } catch (\PDOException $e) {                     // If PDOException was raised                      // Rollback transaction
                                                // For all other reasons
                throw $e;                                // Re-throw exception

        }
    }

    // Get number of search matches
    public function searchCount(string $term): int
    {
        $arguments['term1'] = $arguments['term2'] = $arguments['term3'] = '%' . $term . '%'; // Add wildcards to search term
        $sql   = "SELECT COUNT(title)
                FROM article
               WHERE article.title   LIKE :term1 
                  OR article.summary LIKE :term2 
                  OR article.content LIKE :term3
                 AND article.published = 1;";                      // SQL to count matches
        return $this->db->runSQL($sql, $arguments)->fetchColumn(); // Return number of matches
    }

    // Get article summaries of search matches
    public function search(string $term, int $show = 3, int $from = 0): array
    {
        $arguments['term1'] = $arguments['term2'] = $arguments['term3'] = '%' . $term . '%'; // Add wildcards to search term
        $arguments['show']  = $show;                          // Number of results to show
        $arguments['from']  = $from;                          // Number of results to skip
        $sql  = "SELECT a.id, a.title, a.summary, a.created, a.category_id, a.member_id, a.seo_title,
                        c.name     AS category,
                        c.seo_name AS seo_category,
                        m.userid, m.surname,
                        CONCAT(m.userid, ' ', m.surname) AS author,
                        i.file      AS image_file, 
                        i.alt       AS image_alt,
                        (SELECT COUNT(article_id) 
                           FROM likes 
                          WHERE likes.article_id = a.id) AS likes,
                        (SELECT COUNT(article_id) 
                           FROM comment 
                          WHERE comment.article_id = a.id) AS comments

                   FROM article     AS a
                   JOIN category    AS c    ON a.category_id = c.id
                   JOIN member      AS m    ON a.member_id   = m.id
                   LEFT JOIN image  AS i    ON a.image_id    = i.id

                  WHERE a.title     LIKE :term1 
                     OR a.summary   LIKE :term2
                     OR a.content   LIKE :term3
                    AND a.published = 1
                  ORDER BY a.id DESC
                  LIMIT :show 
                 OFFSET :from;";                                 // SQL to get article summaries
        return $this->db->runSQL($sql, $arguments)->fetchAll();  // Return article summaries
    }

    public function  searchExist(string $service_id)
    {
      intval($service_id);
      $sql = "SELECT (SCHUL_NM) FROM repair WHERE SD_SCHUL_CODE = :service_id;";
      //$argument['service_name'] = $service_name;
      $argument['service_id'] = $service_id;
      return $this->db->runSQL($sql, $argument)->fetch(); // Return data
      
    }


    // ADMIN METHODS
    // Get number of articles
    public function count(): int
    {
        $sql = "SELECT COUNT(id) FROM article;";         // SQL to count articles
        return $this->db->runSQL($sql)->fetchColumn();   // Return count from result set
    }

    // Save new article
    public function createRepair(string $table_name): bool
    {
        try {                                            // Try to insert data
            $this->db->beginTransaction();               // Start transaction
            

            $sql = "CREATE TABLE $table_name (
                    id INT(255) NOT NULL AUTO_INCREMENT,
                    grade VARCHAR(15) NOT NULL,
                    class VARCHAR(15) NULL,
                    request VARCHAR(1000) NOT NULL,
                    status VARCHAR(15) NOT NULL,
                    PRIMARY KEY (id));"; // SQL to add article
            $this->db->runSQL($sql);           // Add article
            $this->db->commit();                         // Commit transaction
            return true;                                 // Return true
        } catch (\PDOException $e) {
            $this->db->rollBack();
            //redirect('exist_school_error'); 
            exit;                 // If PDOException was raised
           
        }
    }

    public function createPrinter(string $table_name): bool
    {
        try {                                            // Try to insert data
            $this->db->beginTransaction();               // Start transaction
            

            $sql = "CREATE TABLE $table_name (
                    id INT(255) NOT NULL AUTO_INCREMENT,
                    grade VARCHAR(15) NOT NULL,
                    class VARCHAR(15) NULL,
                    request VARCHAR(1000) NOT NULL,
                    status VARCHAR(15) NOT NULL,
                    PRIMARY KEY (id));"; // SQL to add article
            $this->db->runSQL($sql);           // Add article
            $this->db->commit();                         // Commit transaction
            return true;                                 // Return true
        } catch (\PDOException $e) {
            $this->db->rollBack();
            redirect('exist_school_error'); 
            exit;                 // If PDOException was raised
           
        }
    }


    public function createCrepair(string $table_name): bool
    {
        try {                                            // Try to insert data
            $this->db->beginTransaction();               // Start transaction
            

            $sql = "CREATE TABLE $table_name (
                    id INT(255) NOT NULL AUTO_INCREMENT,
                    grade VARCHAR(15) NOT NULL,
                    class VARCHAR(15) NULL,
                    request VARCHAR(1000) NOT NULL,
                    status VARCHAR(15) NOT NULL,
                    PRIMARY KEY (id));"; // SQL to add article
            $this->db->runSQL($sql);           // Add article
            $this->db->commit();                         // Commit transaction
            return true;                                 // Return true
        } catch (\PDOException $e) {
            $this->db->rollBack();
            redirect('exist_school_error'); 
            exit;                 // If PDOException was raised
           
        }
    }

        public function createComment(string $table_name): bool
    {
        try {                                            // Try to insert data
            $this->db->beginTransaction();               // Start transaction
            

            $sql = "CREATE TABLE $table_name (
                    id INT(255) NOT NULL AUTO_INCREMENT,
                    service_name VARCHAR(15) NOT NULL,
                    service_id INT(255) NOT NULL,
                    comment_id VARCHAR(1000) NOT NULL,
                    comments VARCHAR(1000) NOT NULL,
                    comment_status VARCHAR(15) NOT NULL,
                    PRIMARY KEY (id));"; // SQL to add article
            $this->db->runSQL($sql);           // Add article
            $this->db->commit();                         // Commit transaction
            return true;                                 // Return true
        } catch (\PDOException $e) {
            $this->db->rollBack();
            print_r($e);
            exit;                 // If PDOException was raised
            if ($e->errorInfo[1] === 1062) {             // If an integrity constraint
                return false;                            // Return false
            } else {                                     // For all other reasons
                throw $e;                                // Re-throw exception
            }
        }
    }

    // Update existing article
    public function insert(string $SCHUL_NM, string $SD_SCHUL_CODE, string $service_name): bool
    {
         try {                                            // Try to insert data
            $this->db->beginTransaction(); 
                $sql = "INSERT INTO repair (SCHUL_NM, SD_SCHUL_CODE, service_name)
                        VALUES (:SCHUL_NM, :SD_SCHUL_CODE, :service_name);"; 
                $arguments['SCHUL_NM']      = $SCHUL_NM;
                $arguments['SD_SCHUL_CODE'] = $SD_SCHUL_CODE;
                $arguments['service_name']  = $service_name;
                $this->db->runSQL($sql, $arguments); 
                $this->db->commit();  
                } catch (\PDOException $e) {
                  $this->db->rollBack();
                  redirect('exist_school_error');  
                  exit;                 // If PDOException was raised
                  if ($e->errorInfo[1] === 1062) {             // If an integrity constraint
                      return false;                            // Return false
                  } else {                                     // For all other reasons
                      throw $e;
                                                    // Re-throw exception
                  }
               }

                                 // Commit transaction
            return true;                                 // Update worked
       
      }

    public function insertPrinter(string $SCHUL_NM, string $SD_SCHUL_CODE, string $service_name): bool
    {
         try {                                            // Try to insert data
            $this->db->beginTransaction(); 
                $sql = "INSERT INTO printer (SCHUL_NM, SD_SCHUL_CODE, service_name)
                        VALUES (:SCHUL_NM, :SD_SCHUL_CODE, :service_name);"; 
                $arguments['SCHUL_NM']      = $SCHUL_NM;
                $arguments['SD_SCHUL_CODE'] = $SD_SCHUL_CODE;
                $arguments['service_name']  = $service_name;
                $this->db->runSQL($sql, $arguments); 
                $this->db->commit();  
                } catch (\PDOException $e) {
                  $this->db->rollBack();
                  redirect('exist_school_error');  
                  exit;                 // If PDOException was raised
                  if ($e->errorInfo[1] === 1062) {             // If an integrity constraint
                      return false;                            // Return false
                  } else {                                     // For all other reasons
                      throw $e;
                                                    // Re-throw exception
                  }
               }

                                 // Commit transaction
            return true;                                 // Update worked
       
      }

      public function insertCrepair(string $SCHUL_NM, string $SD_SCHUL_CODE, string $service_name): bool
    {
         try {                                            // Try to insert data
            $this->db->beginTransaction(); 
                $sql = "INSERT INTO crepair (SCHUL_NM, SD_SCHUL_CODE, service_name)
                        VALUES (:SCHUL_NM, :SD_SCHUL_CODE, :service_name);"; 
                $arguments['SCHUL_NM']      = $SCHUL_NM;
                $arguments['SD_SCHUL_CODE'] = $SD_SCHUL_CODE;
                $arguments['service_name']  = $service_name;
                $this->db->runSQL($sql, $arguments); 
                $this->db->commit();  
                } catch (\PDOException $e) {
                  $this->db->rollBack();
                  redirect('exist_school_error');  
                  exit;                 // If PDOException was raised
                  if ($e->errorInfo[1] === 1062) {             // If an integrity constraint
                      return false;                            // Return false
                  } else {                                     // For all other reasons
                      throw $e;
                                                    // Re-throw exception
                  }
               }

                                 // Commit transaction
            return true;                                 // Update worked
       
      }
    

    // Delete article
    public function delete(int $id): bool
    {
        $sql = "DELETE FROM article WHERE id = :id;";    // SQL statement
        $this->db->runSQL($sql, [$id]);                  // Delete article
        return true;                                     // Return true
    }

    // Delete image from article
    public function imageDelete(int $image_id, string $path, int $article_id): bool
    {
        $sql = "UPDATE article SET image_id = null 
                 WHERE id = :article_id;";               // SQL statement
        $this->db->runSQL($sql, [$article_id]);             // Delete image from article
        $sql = "DELETE FROM image 
                 WHERE id = :id;";                       // SQL statement
        $this->db->runSQL($sql, [$image_id]);            // Delete image from image
        if (file_exists($path)) {                        // If image file exists
            unlink($path);                               // Delete image file
        }
        return true;                                     // Return true
    }

    // Update alt text for article image
    public function altUpdate(int $image_id, string $alt): bool
    {
        $sql = "UPDATE image SET alt = :alt 
                 WHERE id = :article_id;";               // SQL statement
        $this->db->runSQL($sql, [$alt, $image_id]);      // Delete image from article
        return true;                                     // Return true
    }

}